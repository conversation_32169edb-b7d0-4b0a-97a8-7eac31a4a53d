from drill.env import Env
from drill import summary
from raw_env.env_def import *
from raw_env.drone_env import make_drone_env
from typing import Any, DefaultDict, Dict, List, Tuple
from drill.pipeline.interface import ObsData


class DroneFloatEnv(Env):
    """基于drill框架的无人机浮漂检测环境接口"""

    def __init__(self, env_id: int, extra_info: dict):
        self.env_id = env_id
        self._env = make_drone_env()
        self.extra_info = extra_info
        from configs.builder_config import AGENT_NAMES
        self.agent_names = AGENT_NAMES
        print(f"Drone Float Detection Environment {env_id} is ready!")

    def reset(self) -> Any:
        """重置环境

        Returns
        -------
        Any
            环境的初始状态
        """
        obs = self._reset_env()
        obs_dict = {agent_name: ObsData(obs=obs) for agent_name in self.agent_names}
        return obs_dict

    def step(self, command_dict: Dict[str, Any]) -> Tuple[Dict[str, ObsData], bool]:
        """所有参与者依次执行动作

        Parameters
        ----------
        command_dict : Dict[str, Any]
            包含所有参与者的动作

        Returns
        -------
        Tuple[Dict[str, Any], bool]
            obs_dict, done
        """
        # 向仿真端发送命令
        actions = {}
        from configs.builder_config import AGENT_NAMES
        for agent_name in AGENT_NAMES:
            agent_action = command_dict.get(agent_name, None)
            if agent_action is not None:
                # 将drill框架的动作格式转换为环境需要的格式
                actions = self._convert_action(agent_action)
                break  # 单智能体环境，只处理第一个智能体的动作

        # 执行动作
        obs, reward, done, info = self._env.step(actions)

        # 构建返回的观测字典
        obs_dict = {}
        for agent_name in self.agent_names:
            obs_dict[agent_name] = ObsData(
                obs=obs,
                extra_info_dict={
                    "reward": reward,
                    "episode_done": done,
                    **info
                },
                agent_name=agent_name
            )

        # 记录统计信息
        if done:
            summary.average(f"episode_reward_" + self.extra_info['index'], info.get('total_reward', 0))
            summary.average(f"episode_steps_" + self.extra_info['index'], info.get('step_count', 0))
            summary.average(f"detected_floats_" + self.extra_info['index'], info.get('detected_floats', 0))
            summary.average(f"battery_remaining_" + self.extra_info['index'], info.get('battery', 0))
            print(f'Env {self.env_id}: Episode finished - Steps: {info.get("step_count", 0)}, '
                  f'Detected: {info.get("detected_floats", 0)}, Reward: {info.get("total_reward", 0):.2f}')

        return obs_dict, done

    def _reset_env(self):
        """重置为初始状态"""
        obs = self._env.reset()
        return obs

    def _convert_action(self, agent_action: Dict[str, Any]) -> Dict[str, int]:
        """将drill框架的动作格式转换为环境需要的格式"""
        actions = {}

        # 根据网络配置中的动作头名称进行映射
        if 'action_move_fb' in agent_action:
            actions['move_forward_backward'] = agent_action['action_move_fb']
        if 'action_move_lr' in agent_action:
            actions['move_left_right'] = agent_action['action_move_lr']
        if 'action_move_ud' in agent_action:
            actions['move_up_down'] = agent_action['action_move_ud']
        if 'action_rotate' in agent_action:
            actions['rotate_yaw'] = agent_action['action_rotate']
        if 'action_camera_pitch' in agent_action:
            actions['camera_pitch'] = agent_action['action_camera_pitch']
        if 'action_camera_yaw' in agent_action:
            actions['camera_yaw'] = agent_action['action_camera_yaw']

        return actions
