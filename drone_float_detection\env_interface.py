from drill.env import Env
from drill import summary
from raw_env.env_def import *
from raw_env.drone_env import make_drone_env
from typing import Any, DefaultDict, Dict, List, Tuple
from drill.pipeline.interface import ObsData


class DroneFloatEnv(Env):
    """基于drill框架的无人机浮漂检测环境接口"""

    def __init__(self, env_id: int, extra_info: dict):
        self.env_id = env_id
        self._env = make_drone_env()
        self.extra_info = extra_info
        from configs.builder_config import AGENT_NAMES
        self.agent_names = AGENT_NAMES
        print(f"Drone Float Detection Environment {env_id} is ready!")

    def reset(self) -> Any:
        """重置环境

        Returns
        -------
        Any
            环境的初始状态
        """
        obs_dict = self._reset_env()  # 现在返回的是字典
        result = {}
        for agent_name in self.agent_names:
            if agent_name in obs_dict:
                result[agent_name] = ObsData(obs=obs_dict[agent_name])
        return result

    def step(self, command_dict: Dict[str, Any]) -> Tuple[Dict[str, ObsData], bool]:
        """所有参与者依次执行动作

        Parameters
        ----------
        command_dict : Dict[str, Any]
            包含所有参与者的动作

        Returns
        -------
        Tuple[Dict[str, Any], bool]
            obs_dict, done
        """
        # 向仿真端发送命令
        actions = {}
        from configs.builder_config import AGENT_NAMES
        for agent_name in AGENT_NAMES:
            agent_action = command_dict.get(agent_name, None)
            if agent_action is not None:
                # 将drill框架的动作格式转换为环境需要的格式
                actions[agent_name] = self._convert_action(agent_action)

        # 执行动作
        env_obs_dict, reward_dict, done, info = self._env.step(actions)

        # 构建返回的观测字典
        result_obs_dict = {}
        for agent_name in self.agent_names:
            if agent_name in env_obs_dict:
                obs = env_obs_dict[agent_name]
                reward = reward_dict.get(agent_name, 0.0)
                result_obs_dict[agent_name] = ObsData(
                    obs=obs,
                    extra_info_dict={
                        "reward": reward,
                        "episode_done": done,
                        **info
                    },
                    agent_name=agent_name
                )

        # 记录统计信息
        if done:
            summary.average(f"episode_reward_" + self.extra_info['index'], info.get('total_reward', 0))
            summary.average(f"episode_steps_" + self.extra_info['index'], info.get('step_count', 0))
            summary.average(f"interfered_floats_" + self.extra_info['index'], info.get('interfered_floats', 0))
            print(f'Env {self.env_id}: Episode finished - Steps: {info.get("step_count", 0)}, '
                  f'Interfered: {info.get("interfered_floats", 0)}, Reward: {info.get("total_reward", 0):.2f}')

        return result_obs_dict, done

    def _reset_env(self):
        """重置为初始状态"""
        obs = self._env.reset()
        return obs

    def _convert_action(self, agent_action: Dict[str, Any]) -> int:
        """将drill框架的动作格式转换为环境需要的格式"""
        # 根据网络配置中的动作头名称进行映射
        if 'action' in agent_action:
            return int(agent_action['action'])

        return 4  # 默认原地不动
