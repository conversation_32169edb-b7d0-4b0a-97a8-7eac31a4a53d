






仿真服务接口协议
 
1	概述
1.1	接口方式
 
仿真系统通过接入插件实现同外部系统的信息交互。
1. 接入插件对外部提供仿真系统的统一接入能力，一套协议覆盖支持视景系统、AI智能算法、传感器图像生成系统、遥感控制等外部系统或应用。
2. 接入插件对外通过grpc方式实现，接入插件作为grpc的服务端支持多个外部系统或应用以grpc客户端方式接入。采用grpc技术，具有以下突出优势：
	grpc提供了一套协议拟制方法和工具，根据用户拟制的.proto协议文件，能够自动生成c++、c#、python、java、nodejs等多语言代码，开发人员无需重复定义消息报文结构和网络消息收发等底层代码，大大提高了开发效率。
	grpc作为google开源项目，在互联网领域、自动驾驶领域（如百度apollo自动驾驶平台）、军工领域（如南部ZHANQU指挥信息系统）等得到大量应用，其性能和稳定性得到了业界广泛认可。
	grpc 协议具有良好的兼容性，在兼容原有应用的情况下，支持对协议进行持续升级。
1.2	基本约定
1.2.1	坐标系
	全局位置：采用纬经高
	速度、加速度：采用北-东-地，北向为正，东向为正，向地心为正
	机体系：原点是实体的中心，X正半轴指向实体正面，Y正半轴指向实体右侧，Z正半轴指向实体底部。偏航绕Z轴向右为正；滚转绕X轴向右滚转为正；俯仰绕Y轴向右为正，即抬头俯仰角为正。
1.2.2	枚举类型
本协议涉及大量枚举类型，如装备类型、雷达类型、波束类型、弹药类型、引信类型等等，DIS协议对枚举类型进行了详细定义，详见《SISO-REF-010-2023 Enumerations v31》，本协议直接采用。
本协议后续会根据项目中涉及到的枚举类型进行汇总，作为本协议的附录，方便开发人员查询。
1.2.3	GRPC
grpc本质上实现了消息定义、消息序列化、消息反序列化、消息TCP收发等底层数据传输能力，得到了广泛应用。其使用流程如下：
1）按照其语法，编写协议文件，本协议文件为JSRemoteInterface.proto。在proto文件中定义message和service。Message描述了消息结构，包括字段组成、字段类型等。service描述了对外提供的接口，包括流式消息获取接口和单个消息获取接口，前者一次调用就能源源不断的获取消息流，类似实现消息订阅分发功能；后者一次调用立即返回单个消息，类似本地函数调用。
2）使用grpc提供的工具（不同的语言可下载不同的工具），输入proto，生成特定语言的代码，该代码实现了消息定义、序列化、反序列化、发送、接收、回调等。
3）直接集成上述代码，编写自己的业务代码即可。
2	接口详细定义
接口详细定义按照grpc方法进行定义，可直接查看JSRemoteInterface.proto文件，该文件对提供的接口、接口输入输出类型进行了定义，并对定义进行了详细的注释，二次开发人员可直接根据该文件生成所需要的代码进行开发。
本章节仅对JSRemoteInterface.proto进行解释。
2.1	接口汇总
接口汇总表
序号	类型	接口名称	接口描述	输入	输出
1		仿真状态消息流获取	getSimStateStream	1）通过该接口获取仿真状态信息，包括仿真运行状态(SimRunState)、仿真交互事件(FireEvent、DetonationEvent、EntityDeleteEvent)、所有仿真实体的运动状态EntityState
2）该接口只需调用1次，即可源源不断的获取仿真状态信息，直到仿真结束
3）AI算法应用、视景应用、图像生成应用通过该接口可以掌握仿真整体状态，包括当前时间、天气，各仿真实体的位置、姿态、速度、加速度、健康状态 	SimStateTypes：
1）增加权限码字段，默认为AI控制权限，仅能获取红方数据，输入权限码后可获取全部信息，供视景等系统使用。
2）仿真接入服务仅推送用户订阅的仿真状态类型消息。
3)各字段详细描述见.proto	SimRunState：仿真运行环境状态，包括
1）仿真运行、暂停
2）仿真倍速
3）仿真时间
4）仿真天气
订阅后，周期性接收该消息，默认周期为1s。
详见2.2.1节
					EntityState：仿真实体状态，详见2.2.2节
					FireEvent：开火事件，包括发射方、打击方、弹药类型等，详见2.2.3节
					DetonationEvent：爆炸事件，主要内容同FireEvent，增加对爆炸位置和爆炸效果的描述，详见2.2.4节
					EntityDeleteEvent: 实体消失事件详见2.2.5节
2		仿真数据查询	getEntityResourceData	查询指定实体的资源状态	EntityId	ResourceDataList
详见2.3.1
3			getEntitySensorContact	该实体传感器工作状态	EntityId	SensorContactList
详见2.3.2
4			getEntityWeaponData	该实体武器工作状态	EntityId	WeaponDataList
详见2.3.3
5			getDamageState	查询实体毁伤状态	EntityId	详见2.3.4
6			getLaserDesignatorState	获取实体激光照射状态	LaserDesignatorState	laserDesignatorState，详见2.3.6
7			getAeraTerrainHeight	指定区域的高程数据	AreaTerrainHeightRequest	AreaTerrainHeightResponse
详见2.3.5
8			getCurrentTask	查询当前执行任务	EntityId	CurrentTask详见2.3.10
9			getEntitySensors	查询实体传感器	EntityId	SensorList详见2.3.11
10			doesChordHitTerrain	查询两点通视	Points:
点位1（经纬高）
点位2（经纬高）	详见2.10

11		仿真运行控制	loadScenario	加载指定的想定	String  ScenarioPath：想定文件路径	ResultCode：0表示成功，其他为错误码
12			closeScenario	关闭指定的想定	String  ScenarioPath：想定文件路径	ResultCode
13			run	运行指定的想定	String  ScenarioPath：想定文件路径	ResultCode：0表示成功，其他为错误码
14			pause	暂停运行	String  ScenarioPath：想定文件路径	ResultCode：0表示成功，其他为错误码
15			rewind	重置想定	String  ScenarioPath：想定文件路径	ResultCode：0表示成功，其他为错误码
16			setTimeMultiplier	设置仿真倍速	Int TimeMultiplier：指定的仿真倍速	ResultCode：0表示成功，其他为错误码
17			showMessage	向前端发送展示消息	String TextMessage：消息内容	ResultCode：0表示成功，其他为错误码
18		创建控制对象	createEntity	创建实体	entityName：实体名称
entityType：实体DIS编号
forceType：实体所属阵营
location：实体位置
heading：实体朝向
isFire：是否自动开火	ResultCode：返回实体ID表示成功
19			removeEntity	删除实体	entityId：实体ID	ResultCode：0表示成功，其他为错误码
20			createWaypoint	创建航路点，用户需要确保航路点标识的唯一性	Waypoint：指定航路点的位置（纬经高）和唯一标识	ResultCode：0表示成功，其他为错误码
详见2.9
21			createRoute	创建路线，用户需要确保线路标识的唯一性	Route：指定构成路线的多个点位（纬经高）和唯一标识	ResultCode：0表示成功，其他为错误码
22			createPhaseLine	创建阶段线	PhaseLine：指定构成线的两个点位（纬经高）和唯一标识	ResultCode：0表示成功，其他为错误码
23		任务控制	stopMove	停止机动	EntityId：命令接收实体的唯一标识	ResultCode：0表示成功，其他为错误码
24			verticalLand	垂直降落（旋翼机），旋翼机接收该指令后飞到指定降落点并进行垂直降落	moveToWayPointTask：
1）任务执行实体标识；
2）降落点的唯一标识（createWaypoin接口创建时传入的标识）	ResultCode：0表示成功，其他为错误码
25			moveAlongRoute	要求实体沿线路机动	moveAlongRouteTask
1）任务执行实体标识；
2）航线的唯一标识（createRoute接口创建时传入的标识）	ResultCode：0表示成功，其他为错误码
26			moveToLocation	机动到某位置	moveToLocationTask
1）任务执行实体标识；
2）位置（纬经高）	ResultCode：0表示成功，其他为错误码
27			moveToWaypoint	机动到某航路点	moveToWayPointTask
1）任务执行实体标识；
2）目标点的唯一标识（createWaypoin接口创建时传入的标识）	ResultCode：0表示成功，其他为错误码
28			flyHeadingAndAltitude	飞到指定高度和朝向，然后保持该高度和朝向继续飞行	FlyHeadingAndAltitudeTask：
1）任务执行实体标识；
2）期望朝向，与正北夹角，单位弧度
3）转弯速率,弧度/秒
4）期望高度，单位米
5）爬升速率，单位米/秒
6）转弯方向：0表示按照最小转弯角度，1表示右侧拐弯，-1表示左侧拐弯	ResultCode：0表示成功，其他为错误码
29			flyOrbit	绕目标盘旋	OrbitTask：
1）任务执行实体标识
2）盘旋中心位置，纬经高
3）若设置了则绕该目标实体进行盘旋，忽略center的设置
4）飞行半径
5）顺时针或逆时针盘旋	ResultCode：0表示成功，其他为错误码
30			setOrderedSpeed	设置期望速度	SpeedSet：
1）执行该任务的实体标识
2）期望速度大小，单位米/秒	ResultCode：0表示成功，其他为错误码
31			addJammerTarget	将某目标加入电磁干扰目标清单中	TargetTask
1）执行该任务的实体标识
2）目标实体的标识	ResultCode：0表示成功，其他为错误码
32			removeJammerTarget	将某目标从电磁干扰目标清单中去除	TargetTask
1）执行该任务的实体标识
2）目标实体的标识	ResultCode：0表示成功，其他为错误码
33			jammerEnable	电磁干扰启停控制	JammerEnableSet：
1）执行该任务的实体标识
2）启动或停止	ResultCode：0表示成功，其他为错误码
34			fireAtTarget	向某目标开火（使用枪炮或者导弹等直瞄武器系统），不受距离和交战规则限制	FireAtTargetTask：
1）执行该任务的实体
2）指定的武器，可以不指定，不指定则自动选择武器
3）要打击的目标唯一标识
4）射击轮次	ResultCode：0表示成功，其他为错误码
35			setAimSensor	控制传感器视角	AimSensorSet：
1）执行该任务的实体
2）传感器Aim模式，支持指定朝向、指向某点、跟踪某实体、扫描、手动控制五种模式
3）瞄准点，TRACK_LOCATIO 模式下使用
4）瞄准目标实体标识，TRACK_ENTITY模式下使用
5）FIXED_ANGLE使用，瞄准的偏航角，与机体系垂直面夹角6）FIXED_ANGLE使用，瞄准的俯仰角，与机体系水平面夹角	ResultCode：0表示成功，其他为错误码
36			sensorEnable	传感器启停控制	SensorEnableSet：
1）执行该任务的实体
2）启动或停止
3）传感器名称	ResultCode：0表示成功，其他为错误码
37			launchExpendableResource	发射干扰设置，如：箔条	LaunchResource	ResultCode：0表示成功，其他为错误码
38			ffeLocation	间瞄武器打击位置	location：打击位置
weapons：武器类型
numberOfRounds：发射轮数
dispersionRadius：散布半径
heightAboveTerrain：爆炸高度	ResultCode：0表示成功，其他为错误码
39			ffeTarget	间瞄武器打击目标点	target：目标点名称
weapons：武器类型
numberOfRounds：发射轮数
dispersionRadius：散布半径
heightAboveTerrain：爆炸高度	ResultCode：0表示成功，其他为错误码
40			ffeEntity	间瞄武器打击实体	targetEntityId：目标实体ID
weapons：武器类型
numberOfRounds：发射轮数
dispersionRadius：散布半径
heightAboveTerrain：爆炸高度	ResultCode：0表示成功，其他为错误码
2.2	获取仿真状态消息流
调用方法getSimStateStream，实现流式获取仿真状态状态，返回的消息为SimState，SimState是仿真运行环境状态、仿真实体基本状态、开火事件、爆炸事件的一种。
序号	字段名称	字段类型	含义
1		simRunState	SimRunState	仿真运行环境状态
2		entityState	EntityState	仿真实体基本状态
3		fire	FireEvent	开火事件，武器系统发射时发送该消息
4		detonation	DetonationEvent	爆炸事件，弹药爆炸时发送该消息
5		entityDelete	EntityDeleteEvent	仿真实体消失，注意不是仿真实体被摧毁（摧毁的实体还在仿真场景中，以碎片、冒烟、着火等方式展现），而是仿真实体从仿真中消失了，如弹药实体爆炸完后就会从仿真中消失
2.2.1	仿真运行环境状态
用途：仿真运行环境状态包括仿真运行状态、仿真倍速、仿真时间、仿真天气等，订阅后，周期性接收该消息，默认周期为1s。
*******	字段组成
序号	字段名称	字段类型	含义
1		runing	bool	是否在运行，false表示仿真暂停
2		timeMultiplie 	int32	仿真倍速
3		dateAndTimeOfDay 	double	当前秒数，从00:00 hours, Jan 1, 1970 UTC开始算起
4		ambientAirTemperature	float	环境空气温度
5		cloudState	WeatherCloudStateEnum，枚举类型具体包括：
CLEAR = 0 ;      //无云
MOSTLYCLEAR = 1; //少云
CLOUDY = 2;      //有云   
PARTLYCLOUDY = 3; //局部多云
MOSTLYCLOUDY = 4; //大部分多云
OVERCAST = 5;     //阴天多云
THUNDERSTORM= 6;  //雷雨  
SANDSTORM = 7;    //沙尘暴	天气云状态
6		precipitationIntensity	float	降水强度，百分比
7		precipitationType	WeatherPrecipitationEnum，枚举类型具体包括：
  NONE = 0; //无降雨
  RAIN = 1; //下雨
  SNOW = 2; //下雪
  DRYSNOW = 3; //干雪
  SLEET = 4 ; //雨夹雪
  HAIL = 5;  //冰雹  	降水类型
8		windSpeed	float	风速：m/s
9		windDirection	float	风向，与北夹角
10		visibility	float	能见度，单位为米
11		fogHeight	float	雾高度
12		fogColor	FloatV3	雾颜色,rgb表示，数据范围0-1，x=r，y=g，z=b
13		rainAccumulation	float	雨地面积累深度，单位为米
14		snowAccumulation	float	雪地面积累深度，单位为米
15		underwaterVisibility	float	水下能见度，单位为米
16		underwaterVisibility	float	水下能见度，单位为米
17		surfaceTransparency	float	表面透明度，百分比
18		waterCurrentSpeed	float	水流速度 m/s
2.2.2	仿真实体基本状态EntityState
对实体的位置、速度、姿态、加速度、毁伤状态，以及组成部件的运动状态进行描述，该数据发送频率可达50Hz。
竞赛版本中，只有红方数据信息。
2.2.2.1	字段组成
序号	字段名称	字段类型	含义
1		entityId	string	仿真实体唯一标识
2		forceType	ForceTypeEnum，枚举类型，包括：
   FORCE_OTHER = 0;     //其他
   FORCE_FRIEND = 1;    //我方
   FORCE_OPPOSING = 2;  //敌方
   FORCE_NEUTRAL = 3;   //中立方	敌我类别
3		entityType	DisEntityType，遵循DIS协议，由7个字段组成，见2.2.2.2	实体类型
4		Speed	Float	实体线速度
5		velocity 	FloatV3          	仿真实体当前速度，北-东-地，单位为m/s
6		location	FloatV3	x：纬度，y：经度，z：高程
7		orientation	FloatV3Angle	仿真实体当前姿态，单位角度。偏航为机头与正北方向的夹角，向东转为正，俯仰抬头为正，滚转右转为正
8		acceleration	FloatV3	仿真实体当前加速度，北-东-地，单位m/s²
9		angularVelocity	FloatV3	仿真实体当前角速度,，单位为弧度/秒
10		string	markingChars	仿真实体名称（可视化使用）
11		frozen	bool	仿真实体运行状态是否被冻结
12		damageState	DamageStateEnum，枚举类型，包括：
DAMAGE_NONE = 0;   //无毁伤
DAMAGE_SLIGHT = 1; //轻微毁伤
DAMAGE_MODERATE = 2; //中度毁伤
DAMAGE_DESTROYED = 3; //严重损毁	仿真实体毁伤状态
13		agl	float	实体离地高度
14		isEmbarked	bool	是否装载到其他实体
15		embarkedOnEntityId	string	若isEmbarked为true，此字段填写载具实体（如船等）的ID
2.2.2.2	实体类型DisEntityType
遵循DIS标准，实体类型由7个数字组成，其含义如下：
序号	字段名称	字段类型	含义
1		entityKind	EntityKindTypeEnum，枚举类型，包括：
EKT_OTHER_KIND = 0; //其他
EKT_PLATFORM_KIND = 1; //平台
EKT_MUNITION_KIND = 2; //弹药
EKT_LIFEFORM_KIND = 3; //人
EKT_ENVIRONMENTAL_KIND = 4; //环境
EKT_CULTURAL_KIND = 5;//文化社会
EKT_SUPPLY_KIND = 6; //补给
EKT_RADIO_KIND = 7; //电台
EKT_EXPENDABLE_KIND = 8; //体积可扩展
EKT_SENSOR_KIND = 9; //传感器等	平台类型，目前主要使用平台(1)、弹药(2)、人等类型(3)
2		domain	EntityDomainTypeEnum，枚举类型，包括：
EDT_OTHER_DOMAIN = 0;
EDT_LAND_DOMAIN = 1;  //陆
EDT_AIR_DOMAIN = 2;   //空
EDT_SURFACE_DOMAIN = 3;  //海面 
EDT_SUBSURFACE_DOMAIN = 4;  //海底
EDT_SPACE_DOMAIN = 5;       //太空	领域
3		country	uint32	国别编码，中国为45，美国为225，日本为110，印度为99，其他国家可直接查询标准（SISO-REF-2023）5.1.2章
4		category	uint32	大类，如战斗机为1，攻击机为2，轰炸机为3，攻击直升机为20, 可直接查询标准（SISO-REF-2023）5.2.3章
5		subcategory	uint32	子类，如Z-9 为1 ，Z-10 为2
6		specific	uint32	具体型号，如 Z-9W为1
7		extra	uint32	扩展字段
DIS标准（SISO-REF-2023）列出了各国装备的类型标识，本项目直接采用。其中，5.13.22章节对中国装备进行了定义，如Z-9W类型标识为“1.2.45.20.1.1”，Z-10类型为“1.2.45.20.2.1”。
后续根据项目模型集成情况，形成装备类型汇总表。
2.2.3	开火事件FireEvent
对场景中的开火事件进行描述。当场景中出现开火事件时，发送该消息。
2.2.3.1	字段组成
序号	字段名称	字段类型	含义
1		attackerId	string	打击发起方唯一标识
2		targetId	string	打击对象唯一标识，当使用“打击目标”方法时该字段有效
3		munitionId	string	弹药对象唯一标识
4		eventId	string	事件唯一表征
5		velocity	FloatV3	弹药初速，北-东-地，单位为m/s
6		location	DoubleV3	发射位置，x：纬度，y：经度，z：高程
7		munitionType	DisEntityType	弹药类型，参考2.2.2.2类型定义与附录中的类型参考
8		warheadType	WarheadTypeEnum	战斗部类型，非固定项，如有则参考2.2.5.2战斗部类型枚举
9		fuseType	DetonatorFuzeEnum	引信类型，非固定项，如有则参考2.2.5.3引信类型枚举
10		quantity	uint32	连发射击时，总射击次数
11		rate	uint32	连发射击时，射击速率
2.2.3.2	战斗部类型
WARHEAD_Other                     = 0;
WARHEAD_HighExplosive             = 1000;  //高爆弹
WARHEAD_HighExplosivePlastic      = 1100;  //高爆塑性炸药
WARHEAD_HighExplosiveIncendiary   = 1200;  //高爆燃烧弹
WARHEAD_HighExplosiveFragmentation = 1300; //高爆破碎
WARHEAD_HighExplosiveAntiTank     = 1400;  //高爆反坦克
WARHEAD_HighExplosiveBomblets     = 1500;  //高爆小炸弹
WARHEAD_HighExplosiveShapedCharge = 1600;  //高爆成型炸药
WARHEAD_Smoke                     = 2000;  //烟雾弹
WARHEAD_Illumination              = 3000;  //照明弹
WARHEAD_Practice                  = 4000;  //训练弹
WARHEAD_Kinetic                   = 5000;  //运动冲撞
WARHEAD_Unused                    = 6000;  
WARHEAD_Nuclear                   = 7000;  //核
WARHEAD_ChemicalGeneral           = 8000;  //化学
WARHEAD_ChemicalBlisterAgent      = 8100;  //化学起泡剂
WARHEAD_ChemicalBloodAgent        = 8200;  //化学血液试剂
WARHEAD_ChemicalNerveAgent        = 8300;  //化学神经性毒剂
WARHEAD_BiologicalGeneral         = 9000;  //生物
2.2.3.3	引信类型
FUZE_Other            = 0;
FUZE_Contact          = 1000;  //碰炸
FUZE_ContactInstant   = 1100;  //碰炸立即
FUZE_ContactDelayed   = 1200;  //碰炸延迟
FUZE_Timed            = 2000;  //定时
FUZE_Proximity        = 3000;  //近炸
FUZE_Command          = 4000;  //指令
FUZE_Altitude         = 5000;  //定高
FUZE_Depth            = 6000;  //定深
FUZE_Acoustic         = 7000;  //声控
2.2.4	爆炸事件DetonationEvent
对场景中的爆炸事件进行描述。当场景中有爆炸事件产生该消息。
2.2.4.1	字段组成
序号	字段名称	字段类型	含义
1		attackerId	string	打击发起方唯一标识
2		targetId	string	打击对象唯一标识，当使用向目标开火时该字段有效
3		munitionId	string	弹药对象唯一标识
4		eventId	string	事件唯一表征
5		velocity	FloatV3	弹药初速，北-东-地，单位为m/s
6		location	DoubleV3	发射位置，x：纬度，y：经度，z：高程
7		result	DetonationResultEnum	爆炸结果，参考2.2.6.2爆炸结果类型枚举
8		fuseType	DetonatorFuzeEnum	引信类型，非固定项，如有则参考2.2.5.3引信类型枚举
9		munitionType	DisEntityType	弹药类型，参考2.2.2.2类型定义与附录中的类型参考
10		warheadType	WarheadTypeEnum	战斗部类型，非固定项，如有则参考2.2.5.2战斗部类型枚举
11		quantity	uint32	连发射击时，射击次数
12		rate	uint32	连发射击时，射击速率
13		impactLocation	FloatV3	爆炸位置，x：纬度，y：经度，z：高程
2.2.4.2	爆炸结果类型
DET_RES_Other                      = 0;
DET_RES_EntityImpact               = 1;   //打在实体上
DET_RES_EntityProximate            = 2;   //打在实体附近
DET_RES_GroundImpact               = 3;   //打在地面上
DET_RES_GroundProximate            = 4;   //打在地面附近 
DET_RES_Detonation                 = 5;   //引爆
DET_RES_None                       = 6;   //
DET_RES_HE_HitSmall                = 7;   //高爆弹小部分击中
DET_RES_HE_HitMedium               = 8;   //高爆弹部分击中
DET_RES_HE_HitLarge                = 9;   //高爆弹大部分击中
DET_RES_ArmorPiercingHit           = 10;  //碎片击中
DET_RES_DirtBlastSmall             = 11;  //小部分污染
DET_RES_DirtBlastMedium            = 12;  //部分污染
DET_RES_DirtBlastLarge             = 13;  //大部分污染
DET_RES_WaterBlastSmall            = 14;  //喷水
DET_RES_WaterBlastMedium           = 15;  //喷水
DET_RES_WaterBlastLarge            = 16;  //喷水
DET_RES_AirHit                     = 17;  //空中击中
DET_RES_BuildingHitSmall           = 18;  //小部分击中建筑
DET_RES_BuildingHitMedium          = 19;  //部分击中建筑
DET_RES_BuildingHitLarge           = 20;  //大部分击中建筑
DET_RES_MineClearingLineCharge     = 21;  //扫雷线装药
DET_RES_EnvironmentObjectImpact    = 22;  //环境目标击中
DET_RES_EnvironmentObjectProximate = 23;  //环境目标近距离打击
DET_RES_WaterImpact                = 24;  //水面爆
DET_RES_AirBurst                   = 25;  //空爆
2.2.5	仿真实体消失事件EntityDeleteEvent
当仿真实体消失时（如弹药实体爆炸后会自动消失）发送该事件。该消息只有一个字段：  string  entityId = 1;  用来标识消失的仿真实体。
2.2.6	电子自卫告警信息输出事件ElectronicSelfDefenseAlarm
订阅电子自卫告警信息输出流，得到雷达告警信息、激光照射告警信息和导弹逼近告警信息，得到的流信息中通过entityId号区分不同实体，每个实体的告警信息如下。
序号	字段名称	字段类型	含义
1		lotNum	uint32	批号
2		state	uint32	跟踪状态：0：无，1：搜索，2：跟踪，3：锁定
3		platType	uint32	平台型号：0--未知；1--导弹；2--固定翼飞机；3--直升机 4--地面车辆；5--海面舰船
4		iff	uint32	敌我属性0--不明；1--友；2--敌
5		platModel	string	平台型号
6		azi	double	目标相对方位，机体系左-右+
7		ele	double	目标相对俯仰，机体系上+下－
8		isRadarWarning	bool	是否为雷达告警
9		isMissileWarning	bool	是否为导弹逼近告警
10		isLaserWarning	bool	是否为激光照射告警
11		threatLevel	double	威胁等级，越大威胁度越高
2.3	查询指定实体状态
2.3.1	查询指定实体的资源状态
调用方法getEntityResourceData，获取资源数量，实体资源包括实体的油量、弹药等。
输入：实体唯一标识，EntityId
使用限制条件：AI只能查询红方实体。
输出：该实体的资源列表ResourceDataList
ResourceDataList，字段定义如下：
序号	字段名称	字段类型	含义
12		entityId	string	实体的唯一标识
13		resources	repeated ResourceData	ResourceData的列表，一种资源一个ResourceData进行描述
对某仿真实体的资源当前状态进行描述，一个仿真实体可能存在多种资源，每一种资源用ResourceData进行描述，包括资源名称、数量、类型等。如弹药、油量就是一种资源，通过该状态掌握弹药剩余状态、油量剩余状态等信息。
ResourceData字段组成为：
序号	字段名称	字段类型	含义
1		currentAmount	float	当前数量
2		fullAmount	float	满载时数量
3		name	string	资源名称
4		resourceEntityType	DisEntityType	资源实体类型，非固定项，如：燃料无类型，弹药有类型，参考2.2.2.2实体类型
2.3.2	查询实体传感探测信息
调用方法getEntitySensorContact，获取指定实体的传感探测信息。
输入：实体唯一标识，EntityId
使用限制条件：AI只能查询红方实体。
输出：该实体的传感器探测信息SensorContactList
SensorContactList对某仿真实体的传感探测数据进行描述，一个目标使用一个SensorContact进行描述，不包括已严重损毁的目标，定义为：
序号	字段名称	字段类型	含义
1	entityId	string	该传感器所隶属的仿真实体
2	entityType	DisEntityType	仿真实体DIS类型
3	targets	repeated  SensorContact	本传感器发现的目标
其中，传感器目标识别情况SensorContact，字段定义为：
序号	字段名称	字段类型	含义
1		identificationLevel	int32	目标识别等级，1为发现，2为分类，3为识别，4为全知
2		targetId	string	目标ID
3		targetName	string	目标可视化名称
4		speed	float	目标当前线速度，单位为m/s
5		velocity	FloatV3	目标当前矢量速度，北-东-地，单位为m/s
6		location	DoubleV3	目标体当前位置，x：纬度，y：经度，z：高程
7		orientation	FloatV3Angle	目标当前姿态，仿真实体当前姿态，单位角度。偏航为机头与正北方向的夹角，向东转为正，俯仰抬头为正，滚转右转为正
8		entityType	DisEntityType	目标装备类型
9		targetAzimuth	double	目标方位角（当且仅当有且仅有ESM探测到目标时该字段有值）
10		targetElevation	double	目标俯仰角（当且仅当有且仅有ESM探测到目标时该字段有值）
目标识别等级与返回信息的关系如下：
	1：返回目标ID、目标当前位置、目标装备类型的平台种类和领域，即DIS编号前两位；
	2：在1级的基础上，增加目标当前线速度；
	3：在2级的基础上，增加目标当前矢量速度，增加目标装备类型的国别和大类，即DIS编号3,4两位；
	4：在3级的基础上，增加目标可视化名称、姿态、装备类型的全部信息。
2.3.3	查询武器状态
调用方法getEntityWeaponData，获取指定实体的武器状态。
输入：实体唯一标识，EntityId
使用限制条件：AI只能查询红方实体。
输出：该实体的武器状态信息WeaponDataList，字段定义如下：
序号	字段名称	字段类型	含义
1	entityId	string	该传感器所隶属的仿真实体
2	weapons	repeated WeaponData	武器状态列表，有几个武器就有几个WeaponData
对某仿真实体的武器信息进行描述，每一个武器由一个WeaponData进行描述，包括武器名称、类型、所使用的资源。其中武器类型由WeaponSystemTypeEnum进行定义，包括直瞄、间瞄、炸弹、导弹等。
WeaponData字段组成为：
序号	字段名称	字段类型	含义
1		weaponName	string	武器标识
2		weaponType	WeaponSystemTypeEnum，枚举类型，包括：
WEAPON_ALLTYPE = 0;   WEAPON_DIRECTFIRE_BALLISTIC = 1; //直瞄弹道武器：枪、航炮等，可以指定具体目标的   
WEAPON_DIRECTFIRE_LAUNCHER  = 2; //导弹
WEAPON_INDIRECT_ARTILLERY = 3;   //间瞄武器，排炮、榴弹炮
WEAPON_INDIRECT_BOMB = 4;        //航空炸弹	武器类型
3		status	WeaponStatusEnum：
    OK_TO_FIRE = 0;   //就绪可以开火
    DISABLED  = 1;   //武器系统关闭，无法使用
    NO_AMMUNITION_FOR_TARGET = 2;  //针对该目标，无可用弹药
    CANNOT_ACQUIRE_TARGET    = 4;  //与目标不通视（对于直线弹道才会返回）
    SUPPRESSED               = 8;  //被压制，无法开火
    OUT_OF_AMMUNITION_FOR_WEAPON  = 16; //弹药耗光 	武器状态
4		resources	repeated  string	该武器所对应的资源名称
2.3.4	查询实体毁伤状态
调用方法getDamageState，获取指定实体的毁伤状态。
输入：实体唯一标识，EntityId
输出：该实体毁伤状态DamageState，定义为
序号	字段名称	字段类型	含义
1	damageState	DamageStateEnum，枚举类型，包括：
   DAMAGE_NONE = 0;    //无毁伤
   DAMAGE_SLIGHT = 1;   //轻微毁伤
   DAMAGE_MODERATE = 2; //中度毁伤
   DAMAGE_DESTROYED = 3; //严重损毁	该实体的毁伤状态
2.3.5	查询已销毁实体列表
序号	字段名称	字段类型	含义
1	entityId	repeated string	已销毁仿真实体ID列表

2.3.6	查询实体具体类型描述
序号	字段名称	字段类型	含义
1	entityId	string	仿真实体ID
2	desc	string	实体具体类型描述
2.3.7	查询实体传感器
序号	字段名称	字段类型	含义
1	entityId	string	仿真实体ID
2	name	repeated string	传感器列表
3	enable	bool	true为启用

2.4	跳过当前任务
调用方法skipTask，传入entityId指定需要停止任务的实体，传入taskName将停止taskName对应的任务。
输入：TaskName	
序号	字段名称	字段类型	含义
1	entityId	string	执行该任务的实体
2	taskName	string	任务名称
2.5	机动任务
2.5.1	创建航点
调用方法createWaypoin，在指定位置创建航路点，创建成功后，在平台态势界面中显示该创建的航点。
输入：航点Waypoint
序号	字段名称	字段类型	含义
1		nameId	string	航点的唯一标识，最多支持10个字符
2		location	DoubleV3	航点位置，纬经高
2.5.2	创建航线
调用方法createRoute，在多个指定的位置创建航线，创建成功后，在平台态势界面中显示该创建的航线。
输入：Route
序号	字段名称	字段类型	含义
1		nameId	string	航线的唯一标识，最多支持10个字符
2		locations	repeated  DoubleV3	航点列表，纬经高
2.5.3	创建阶段线
调用方法createPhaseLine，阶段线为两点构成的直线，创建成功后，在平台态势界面中显示该创建的阶段。
输入：PhaseLine
序号	字段名称	字段类型	含义
1		nameId	string	唯一标识，最多支持10个字符
2		location1	DoubleV3	第一个点，纬度、经度、海拔高
3		location2	DoubleV3	第二个点，纬度、经度、海拔高
2.5.4	停止机动
调用方法stopMove，指定的实体将停止当前机动任务，将速度降低为0，保持悬停。
输入：EntityId
序号	字段名称	字段类型	含义
1	entityId	string	执行该任务的实体
2.5.5	飞往指定航路点
调用方法moveToWaypoint，指定的仿真实体将飞往要求的航路点，提前减速确保到达目的后速度降低为0。
输入：moveToWayPointTask
序号	字段名称	字段类型	含义
1		entityId	string	执行该任务的实体
2		waypointId	string	航路点唯一标识，调用createWaypoin时指定的名称
2.5.6	飞往指定经纬度
调用方法moveToLocation，指定的仿真实体将飞往要求的位置。提前减速确保到达目的后速度降低为0。
输入：moveToLocationTask
序号	字段名称	字段类型	含义
1		entityId	string	执行该任务的实体
2		location	DoubleV3	目的地，纬经高
2.5.7	沿航线飞行
调用方法moveAlongRoute，指定的仿真实体将按要求的航线飞行。提前减速确保到达最后一个航点后速度降低为0。
输入：moveAlongRouteTask
序号	字段名称	字段类型	含义
1	entityId	string	执行该任务的实体
2	routeId	string	createRoute时指定的航线名称
2.5.8	设定期望飞行速度
调用方法setOrderedSpeed，指定的仿真实体将按要求的飞行速度飞行。
输入：SpeedSet
序号	字段名称	字段类型	含义
1	entityId	string	执行该任务的实体
2	speed	float	期望飞行速度，单位米/秒 
2.5.9	垂直降落
调用方法verticalLand，垂直降落（旋翼机），旋翼机接收该指令后飞到指定降落点并进行垂直降落指定的仿真实体将按要求的航线降落。将按要求的飞行速度飞行。
输入：moveToWayPointTask
序号	字段名称	字段类型	含义
1	entityId	string	执行该任务的实体
2	waypointId	string	航路点唯一标识，调用createWaypoin时指定的名称
2.5.10	飞往指定高度和方位
调用方法flyHeadingAndAltitude，指定的实体将按照要求的朝向、高度进行飞行。
输入：要求的飞行高度和姿态，FlyHeadingAndAltitudeTask
序号	字段名称	字段类型	含义
1		entityId	string	执行该任务的实体
2		heading	float  	期望朝向，与北方的夹角，单位弧度
3		turnRate 	float  	转弯速率, 弧度/秒
4		altitude	float	期望高度，单位米
5		climbDescentRate	float  	爬升速率，单位米/秒
6		turnDirection	sint32  	转弯方向：0表示按照最小转弯角度，1表示右侧拐弯，-1表示左侧拐弯
2.5.11	盘旋飞行
调用方法flyOrbit，实现绕指定位置和半径盘旋	飞行。
输入：盘旋飞行OrbitTask
序号	字段名称	字段类型	含义
1		entityId	string      	执行该任务的实体
2		center	DoubleV3	盘旋中心位置，纬经高
3		radius	float	飞行半径
4		clockwise	bool	true为顺时针盘旋，false为逆时针盘旋
2.6	打击任务
2.6.1	创建打击目标点
调用方法createTargetPoint，在指定的位置创建一个打击目标点虚拟实体，同时在态势图上以十字型进行展现。
输入：TargetPoint
序号	字段名称	字段类型	含义
1		nameId	string	打击目标点的唯一标识，最多支持10个字符
2		location	DoubleV3	航点位置，纬经高
2.6.2	向目标开火
调用方法fireAtTarget，向某目标开火（使用枪炮或者导弹等直瞄武器系统），不受距离和交战规则限制。
使用限制条件：targetId必须在所有红方实体的传感器探测目标结果里，或者是由createTargetPoint方法创建的打击目标点。
输入：向目标开火FireAtTargetTask
序号	字段名称	字段类型	含义
1		entityId	string            	执行该任务的实体
2		weaponToFire	string            	指定的武器，可以不指定，不指定则自动选择武器（按照模型配置工具里武器系统的配置顺序进行选择）
3		targetId  	string            	如果是打击某仿真实体，填目标实体的唯一标识ID；
如果是打击的某位置，填createTargetPoint为该位置创建的打击目标点唯一标识nameId
4		maxRoundsToFire	int32             	射击轮次，当使用枪炮武器系统时设置
2.6.3	释放干扰弹
调用方法launchExpendableResource，发射干扰设置，如：箔条。
输入：LaunchResource
序号	字段名称	字段类型	含义
1		entityId	string	执行该任务的实体
2		name	string	干扰弹名称
3		number	int32	数量
4		timeInterval	double	间隔
2.7	电磁干扰
2.7.1	电子干扰系统启动/停止
调用方法jammerEnable，对指定实体的电磁干扰系统的启停控制。
输入：JammerEnableSet
序号	字段名称	字段类型	含义
1		entityId	string      	执行该任务的实体
2		enanble    	bool	true为启动，false为关闭
2.7.2	增加电磁干扰目标
调用方法addJammerTarget，将指定目标添加到电磁干扰目标清单中。
输入：TargetTask
序号	字段名称	字段类型	含义
1		entityId	string      	执行该任务的实体
2		targetId	string      	目标唯一标识ID
2.7.3	删除电磁干扰目标
调用方法removeJammerTarget，将指定目标从电磁干扰目标清单中删除。
输入：TargetTask
序号	字段名称	字段类型	含义
1		entityId	string      	执行该任务的实体
2		targetId	string      	目标唯一标识ID
2.8	传感器控制
2.8.1	传感器视角控制
调用方法setAimSensor，对光学传感器进行视角控制。
输入：AimSensorSet
序号	字段名称	字段类型	含义
1.		entityId	string      	执行该任务的实体
2.		mode	SensorAimModeEnum，工作模式，枚举类型：
 FIXED_ANGLE = 0;     // 该模式下，固定aimingAz和aimingEle
 TRACK_ENTITY = 1;    // 该模式下，跟踪实体
 TRACK_LOCATION = 2;  // 该模式下，一致指向某位置
 SCAN = 3;            // 该模式下，来回扫描
MANUAL = 4;          // 该模式下由用户手动控制	传感器Aim模式
3.		aimingPoint  	DoubleV3	瞄准点，TRACK_LOCATIO 模式下使用
4.		trackEntityId	string  	瞄准目标实体标识，TRACK_ENTITY模式下使用
5.		aimingAz  	DoubleV3	FIXED_ANGLE使用，瞄准的偏航角，与机体系垂直面夹角
6.		aimingEle  	DoubleV3	FIXED_ANGLE使用，瞄准的俯仰角，与机体系水平面夹角
2.8.2	传感器启停控制
调用方法sensorEnable，对指定实体的传感器启停进行控制。
输入：SensorEnableSet
序号	字段名称	字段类型	含义
1.		entityId	string      	执行该任务的实体
2.		enanble    	bool        	启动或停止，true为启动，false为关闭
3.		sensorName	string      	传感器名称
2.9	地形两点通视
调用方法doesChordHitTerrain，判断两点之间能否通视，Visibility:true表示能通视。
输入：Points	
序号	字段名称	字段类型	含义
1	Point1	DoubleV3	通视第一个点
2	Point2	DoubleV3	通视第二个点

3	开发示例
3.1	建立链接
import grpc
import JSRemoteInterface_pb2
import JSRemoteInterface_pb2_grpc
channel = grpc.insecure_channel("***********:50051")
stub = JSRemoteInterface_pb2_grpc.JSRemoteControlStub(channel)
注释：
1）导入grpc接口库
2）创建链接channel，输出参数为“IP地址：端口”，其中IP地址为仿真平台计算机地址，端口为50051
3）Grpc默认单包数据收发大小上限为4MB，调整上限的方式如下所示，调整至100MB：
 
3.2	接收仿真状态消息流
一般可以通过启动线程进行数据接收，然后将接收到的数据存储到全局变量中。
//仿真状态全局变量
g_entityStateMap = {} #用于存放接收到的红方实体状态，采用字典方式，key为仿真实体的名称，value为仿真实体的详细状态
g_simState = NULL #仿真运行状态
g_denotioneventMap = {} #爆炸事件字典，key可以定义为”打击发起方:目标”，方便索引
//仿真状态处理函数
# 源源不断的接收仿真平台发送的仿真状态消息
def getSimState():
    global g_entityStateMap
    global g_simState
    global g_denotioneventMap
stud = JSRemoteInterface_pb2_grpc.JSRemoteControlStub(channel)
//进入循环处理消息流
    for resp in stud.getSimStateStream(JSRemoteInterface_pb2.SimStateTypes()):
        if(resp.HasField('simRunState')):
            g_simState = resp.simRunState
        if(resp.HasField('entityState')):
		//以resp.entityState.markingChars为key进行索引
            g_entityStateMap[resp.entityState.markingChars] = resp.entityState           
        if(resp.HasField('detonation')):
          //以resp.detonation.attackerId+resp.detonation.targetId为key进行索引
     g_denotioneventMap[resp.detonation.attackerId+resp.detonation.targetId] = resp.detonation
//启动新的线程进行仿真消息数据处理
threading.Thread(target=getSimState).start()
3.3	方法调用
方法调用模式是一致的，首先构建输入结构体，调用方法，处理输出。以飞往某位置为例：
req = JSRemoteInterface_pb2.moveToLocationTask()
    # 执行该任务实体id 如：1:3000:10
    req.entityId = “1:3000:10”
req.location.x = 纬度
req.location.y = 经度
    req.location.z = 海拔高
    response = stub.moveToLocation(req)
