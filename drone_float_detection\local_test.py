#!/usr/bin/env python3
"""
无人机浮漂干扰项目本地测试脚本
基于drill框架的简化版本
"""

import sys
import os
import numpy as np
import time

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from raw_env.drone_env import make_drone_env
from raw_env.env_def import ACTIONS


def test_basic_env():
    """测试基础环境功能"""
    print("=== 测试基础环境功能 ===")

    # 创建环境
    env = make_drone_env()

    # 重置环境
    obs = env.reset()
    print(f"初始观测: {obs}")

    # 测试几步随机动作
    for step in range(10):
        action = np.random.randint(0, 5)  # 随机选择动作
        action_name = ACTIONS[action]

        obs, reward, done, info = env.step(action)

        print(f"步骤 {step+1}:")
        print(f"  动作: {action} ({action_name})")
        print(f"  无人机位置: {obs['drone']['position']}")
        print(f"  奖励: {reward:.2f}")
        print(f"  干扰浮漂数: {info['interfered_floats']}")
        print(f"  是否结束: {done}")
        print()

        if done:
            print("Episode结束!")
            break

    print("基础环境测试完成!\n")


def test_action_effects():
    """测试动作效果"""
    print("=== 测试动作效果 ===")

    env = make_drone_env()
    obs = env.reset()

    initial_pos = obs['drone']['position'].copy()
    print(f"初始位置: {initial_pos}")

    # 测试每个动作
    for action in range(5):
        env.reset()
        obs = env.reset()
        initial_pos = obs['drone']['position'].copy()

        obs, reward, done, info = env.step(action)
        new_pos = obs['drone']['position']

        print(f"动作 {action} ({ACTIONS[action]}):")
        print(f"  位置变化: {initial_pos} -> {new_pos}")
        print(f"  位移: {new_pos - initial_pos}")
        print()

    print("动作效果测试完成!\n")


def test_interference():
    """测试干扰机制"""
    print("=== 测试干扰机制 ===")

    env = make_drone_env()
    obs = env.reset()

    print(f"浮漂数量: {len(obs['floats'])}")
    print("浮漂位置:")
    for i, float_info in enumerate(obs['floats']):
        print(f"  浮漂 {i}: {float_info['position']}")

    print(f"\n无人机初始位置: {obs['drone']['position']}")

    # 尝试移动到第一个浮漂附近
    target_float = obs['floats'][0]
    target_pos = target_float['position']
    drone_pos = obs['drone']['position']

    print(f"目标浮漂位置: {target_pos}")
    print(f"初始距离: {np.linalg.norm(target_pos - drone_pos):.2f}")

    # 简单的移动策略：向目标移动
    for step in range(20):
        current_pos = obs['drone']['position']
        diff = target_pos - current_pos

        # 选择最佳移动方向
        if abs(diff[0]) > abs(diff[1]):
            if diff[0] > 0:
                action = 3  # 右
            else:
                action = 2  # 左
        else:
            if diff[1] > 0:
                action = 0  # 上
            else:
                action = 1  # 下

        obs, reward, done, info = env.step(action)
        new_pos = obs['drone']['position']
        distance = np.linalg.norm(target_pos - new_pos)

        print(f"步骤 {step+1}: 动作={ACTIONS[action]}, 位置={new_pos}, 距离={distance:.2f}, 奖励={reward:.2f}")

        if reward > 0:
            print("  -> 成功干扰!")

        if done:
            break

    print("干扰机制测试完成!\n")


def test_episode():
    """测试完整episode"""
    print("=== 测试完整episode ===")

    env = make_drone_env()

    for episode in range(3):
        print(f"\n--- Episode {episode + 1} ---")
        obs = env.reset()
        total_reward = 0
        step_count = 0

        while True:
            # 简单的随机策略
            action = np.random.randint(0, 5)
            obs, reward, done, info = env.step(action)

            total_reward += reward
            step_count += 1

            if step_count % 10 == 0:
                print(f"  步骤 {step_count}: 累计奖励={total_reward:.2f}, 干扰数={info['interfered_floats']}")

            if done:
                print(f"Episode结束: 总步数={step_count}, 总奖励={total_reward:.2f}, 干扰浮漂数={info['interfered_floats']}")
                break

    print("完整episode测试完成!\n")


def main():
    """主函数"""
    print("开始无人机浮漂干扰环境测试...")
    print("=" * 50)

    try:
        # 运行各项测试
        test_basic_env()
        test_action_effects()
        test_interference()
        test_episode()

        print("=" * 50)
        print("所有测试完成!")

    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
