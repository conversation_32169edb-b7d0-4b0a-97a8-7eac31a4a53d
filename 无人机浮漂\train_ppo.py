import numpy as np
import torch
import matplotlib.pyplot as plt
from environment import Environment
from ppo_agent import PPOAgent, Memory, InterferenceModeSelector
import time
import os

# 状态特征提取
def extract_state_features(env, drone_id):
    """
    为指定无人机提取状态特征
    
    特征包括:
    1. 无人机自身位置 (x, y)
    2. 无人机到地图边界的距离 (4个值)
    3. 附近浮漂的相对位置 (最近10个浮漂，每个2个值)
    4. 附近通信对的信息 (最近5个通信对，每个6个值：浮漂1相对位置，浮漂2相对位置，通信质量，通信模式)
    """
    # 获取无人机位置
    drone_name = f"无人机{drone_id}"
    dx, dy = env.drone_positions[drone_name]
    
    # 计算到边界的距离
    dist_to_left = dx
    dist_to_right = env.map_width - dx
    dist_to_bottom = dy
    dist_to_top = env.map_height - dy
    
    # 计算到所有浮漂的距离
    buoy_distances = []
    for buoy in env.buoy_positions:
        bx, by = buoy["x"], buoy["y"]
        dist = ((dx - bx) ** 2 + (dy - by) ** 2) ** 0.5
        rel_x = (bx - dx) / env.map_width  # 归一化相对位置
        rel_y = (by - dy) / env.map_height
        buoy_distances.append((dist, rel_x, rel_y, buoy["id"]))
    
    # 选择最近的10个浮漂
    buoy_distances.sort(key=lambda x: x[0])
    closest_buoys = buoy_distances[:10]
    # 如果不足10个，用(0,0)填充
    while len(closest_buoys) < 10:
        closest_buoys.append((0, 0, 0, -1))
    
    # 提取通信对信息
    comm_info = []
    for pair in env.comm_pairs:
        buoy1 = next(b for b in env.buoy_positions if b["id"] == pair["buoy1"])
        buoy2 = next(b for b in env.buoy_positions if b["id"] == pair["buoy2"])
        
        # 计算无人机到两个浮漂的距离
        dist1 = ((dx - buoy1["x"]) ** 2 + (dy - buoy1["y"]) ** 2) ** 0.5
        dist2 = ((dx - buoy2["x"]) ** 2 + (dy - buoy2["y"]) ** 2) ** 0.5
        
        # 计算浮漂相对于无人机的位置
        rel_x1 = (buoy1["x"] - dx) / env.map_width
        rel_y1 = (buoy1["y"] - dy) / env.map_height
        rel_x2 = (buoy2["x"] - dx) / env.map_width
        rel_y2 = (buoy2["y"] - dy) / env.map_height
        
        # 通信质量
        quality = pair["quality"] / 100.0  # 归一化
        
        # 通信模式
        mode = pair["mode"] / 19.0  # 归一化
        
        min_dist = min(dist1, dist2)
        comm_info.append((min_dist, rel_x1, rel_y1, rel_x2, rel_y2, quality, mode))
    
    # 选择最近的5个通信对
    comm_info.sort(key=lambda x: x[0])
    closest_comms = comm_info[:5]
    # 如果不足5个，用0填充
    while len(closest_comms) < 5:
        closest_comms.append((0, 0, 0, 0, 0, 0, 0))
    
    # 组合所有特征
    features = [
        dx / env.map_width,  # 归一化位置
        dy / env.map_height,
        dist_to_left / env.map_width,
        dist_to_right / env.map_width,
        dist_to_bottom / env.map_height,
        dist_to_top / env.map_height
    ]
    
    # 添加最近浮漂的相对位置
    for _, rel_x, rel_y, _ in closest_buoys:
        features.extend([rel_x, rel_y])
    
    # 添加通信对信息
    for _, rel_x1, rel_y1, rel_x2, rel_y2, quality, mode in closest_comms:
        features.extend([rel_x1, rel_y1, rel_x2, rel_y2, quality, mode])
    
    return np.array(features, dtype=np.float32)

# 计算点到线段的最短距离
def point_to_line_segment_distance(dx, dy, x1, y1, x2, y2):
    """
    计算点(dx,dy)到线段(x1,y1)-(x2,y2)的最短距离
    """
    # 计算向量
    line_vec = (x2 - x1, y2 - y1)
    drone_to_buoy1_vec = (dx - x1, dy - y1)
    
    # 计算线段长度的平方
    line_length_squared = line_vec[0]**2 + line_vec[1]**2
    
    # 如果线段长度为0（两个浮漂位置相同），直接计算点到点距离
    if line_length_squared == 0:
        return ((dx - x1)**2 + (dy - y1)**2)**0.5
    
    # 计算投影比例
    t = max(0, min(1, (drone_to_buoy1_vec[0]*line_vec[0] + drone_to_buoy1_vec[1]*line_vec[1]) / line_length_squared))
    
    # 计算投影点
    proj_x = x1 + t * line_vec[0]
    proj_y = y1 + t * line_vec[1]
    
    # 计算距离
    return ((dx - proj_x)**2 + (dy - proj_y)**2)**0.5

# 计算奖励函数
def calculate_reward(env, prev_comm_quality, curr_comm_quality, drone_id):
    """
    计算奖励函数
    
    奖励组成:
    1. 通信质量提升奖励
    2. 干扰效果奖励
    3. 覆盖更多通信对的奖励
    """
    # 计算通信质量变化
    prev_total_quality = sum([p["quality"] for p in prev_comm_quality])
    curr_total_quality = sum([p["quality"] for p in curr_comm_quality])
    quality_change = curr_total_quality - prev_total_quality
    
    # 获取环境中所有通信对的总数
    total_pairs_count = len(curr_comm_quality)
    
    # 通信质量变化奖励 - 归一化并缩放
    # 将通信质量变化归一化到[-1, 1]范围
    # 我们假设通信质量的最大变化可能是所有通信对的总质量(假设最大质量为100)
    max_possible_change = 100 * total_pairs_count
    normalized_quality_change = quality_change / max_possible_change if max_possible_change > 0 else 0
    quality_reward = -normalized_quality_change * 1.5  # 通信质量下降越多，奖励越高，权重增加
    
    # 计算该无人机的干扰效果
    drone_name = f"无人机{drone_id}"
    dx, dy = env.drone_positions[drone_name]
    drone_mode = env.drone_modes[drone_name]
    
    interference_effect = 0
    affected_pairs_count = 0
    
    for pair in env.comm_pairs:
        buoy1 = next(b for b in env.buoy_positions if b["id"] == pair["buoy1"])
        buoy2 = next(b for b in env.buoy_positions if b["id"] == pair["buoy2"])
        
        # 计算无人机到两个浮漂的距离
        dist1 = ((dx - buoy1["x"]) ** 2 + (dy - buoy1["y"]) ** 2) ** 0.5
        dist2 = ((dx - buoy2["x"]) ** 2 + (dy - buoy2["y"]) ** 2) ** 0.5
        
        # 计算无人机到通信链路的距离
        dist_to_link = point_to_line_segment_distance(dx, dy, buoy1["x"], buoy1["y"], buoy2["x"], buoy2["y"])
        
        # 如果至少一个浮漂在干扰范围内或通信链路穿过干扰范围
        if dist1 <= env.interference_range or dist2 <= env.interference_range or dist_to_link <= env.interference_range:
            comm_mode = pair["mode"]
            effect = env.interference_table[drone_mode-1][comm_mode]
            interference_effect += effect
            affected_pairs_count += 1
    
    # 干扰效果奖励 - 归一化并缩放
    # 假设干扰效果的最大值是1.0，最大总干扰效果是受影响通信对的数量
    max_possible_effect = affected_pairs_count
    normalized_interference = interference_effect / max_possible_effect if max_possible_effect > 0 else 0
    interference_reward = normalized_interference * 1.2  # 增加干扰效果的权重
    
    # 覆盖通信对数量奖励 - 归一化并缩放
    # 归一化为覆盖比例（覆盖的通信对/总通信对）
    coverage_ratio = affected_pairs_count / total_pairs_count if total_pairs_count > 0 else 0
    coverage_reward = coverage_ratio * 1.0
    
    # 总奖励 - 各部分权重可以根据需要调整
    total_reward = quality_reward + interference_reward + coverage_reward
    
    return total_reward

# 训练函数
def train(env, max_episodes=4000, max_timesteps=250, update_timestep=1000, 
    log_interval=10, save_interval=50):
    # 状态维度和动作维度
    state_dim = 6 + 10*2 + 5*6  # 6个基本特征 + 10个浮漂*2 + 5个通信对*6
    action_dim = 5  # 上、下、左、右、原地不动
    
    # 初始化PPO代理
    agent = PPOAgent(state_dim, action_dim, lr=3e-4, gamma=0.99, eps_clip=0.2, K_epochs=10)
    memory = Memory()
    
    # 初始化干扰模式选择器
    mode_selector = InterferenceModeSelector(env.interference_table)
    
    # 训练记录
    training_records = []
    
    # 创建保存模型的目录
    if not os.path.exists('models'):
        os.makedirs('models')
    
    # 开始训练
    print("开始训练...")
    timestep = 0
    
    for episode in range(1, max_episodes+1):
        env_info = env.reset()
        episode_reward = 0
        step_count = 0
        
        for t in range(max_timesteps):
            timestep += 1
            step_count += 1
            
            # 存储每个无人机的动作
            actions = {}
            
            # 为每个无人机选择动作
            for i in range(1, env.num_drones+1):
                drone_name = f"无人机{i}"
                
                # 提取状态
                state = extract_state_features(env, i)
                
                # 选择动作
                action, log_prob = agent.select_action(state)
                
                # 存储到内存
                memory.states.append(state)
                memory.actions.append(action)
                memory.log_probs.append(log_prob)
                
                actions[i] = action
            
            # 选择最佳干扰模式
            best_modes = mode_selector.select_best_mode(
                env.drone_positions, 
                env.buoy_positions, 
                env.comm_pairs,
                env.interference_range
            )
            
            # 组合动作和干扰模式
            for i in range(1, env.num_drones+1):
                drone_name = f"无人机{i}"
                actions[i] = (actions[i], best_modes[drone_name])
            
            # 记录当前通信质量
            prev_comm_quality = env.comm_pairs.copy()
            
            # 执行动作
            drone_result, buoys, comms, _ = env.step(actions)
            
            # 计算每个无人机的奖励
            step_reward = 0
            for i in range(1, env.num_drones+1):
                reward = calculate_reward(env, prev_comm_quality, comms, i)
                memory.rewards.append(reward)
                memory.is_terminals.append(False)
                step_reward += reward / env.num_drones  # 平均到每个无人机
            
            episode_reward += step_reward  # 累加每步的平均奖励
            
            # 如果达到更新时间步
            if timestep % update_timestep == 0:
                agent.update(memory)
                memory.clear_memory()
                timestep = 0
                
            # 如果达到最大时间步，标记最后的状态为终止状态
            if t == max_timesteps-1:
                # 修复索引错误：确保有足够的元素可以标记
                # 只标记最后env.num_drones个状态为终止状态，且确保不超出列表长度
                for i in range(min(env.num_drones, len(memory.is_terminals))):
                    if i < len(memory.is_terminals):
                        memory.is_terminals[-i-1] = True
        
        # 计算平均每步奖励，避免回合长度不同造成的不公平比较
        episode_reward = episode_reward / step_count if step_count > 0 else 0
        
        # 记录训练结果
        training_records.append(episode_reward)
        
        # 打印训练进度
        if episode % log_interval == 0:
            print(f"Episode {episode}\tAverage Reward: {np.mean(training_records[-log_interval:]):.3f}")
        
        # 保存模型
        if episode % save_interval == 0:
            torch.save(agent.policy.state_dict(), f'models/drone_ppo_episode_{episode}.pth')
    
    # 保存最终模型
    torch.save(agent.policy.state_dict(), 'models/drone_ppo_final.pth')
    
    # 绘制训练曲线
    plt.figure(figsize=(10, 6))
    plt.plot(training_records)
    plt.title('PPO训练曲线')
    plt.xlabel('回合')
    plt.ylabel('平均奖励')
    plt.savefig('training_curve.png')
    plt.close()
    
    return agent

# 测试函数
def test(env, agent, mode_selector, num_episodes=10, max_timesteps=500):
    print("开始测试...")
    
    for episode in range(1, num_episodes+1):
        env_info = env.reset()
        episode_reward = 0
        step_count = 0
        
        for t in range(max_timesteps):
            step_count += 1
            # 存储每个无人机的动作
            actions = {}
            
            # 为每个无人机选择动作
            for i in range(1, env.num_drones+1):
                drone_name = f"无人机{i}"
                
                # 提取状态
                state = extract_state_features(env, i)
                
                # 选择动作
                with torch.no_grad():
                    state = torch.FloatTensor(state)
                    action_probs, _ = agent.policy(state)
                    action = torch.argmax(action_probs).item()
                
                actions[i] = action
            
            # 选择最佳干扰模式
            best_modes = mode_selector.select_best_mode(
                env.drone_positions, 
                env.buoy_positions, 
                env.comm_pairs,
                env.interference_range
            )
            
            # 组合动作和干扰模式
            for i in range(1, env.num_drones+1):
                drone_name = f"无人机{i}"
                actions[i] = (actions[i], best_modes[drone_name])
            
            # 记录当前通信质量
            prev_comm_quality = env.comm_pairs.copy()
            
            # 执行动作
            drone_result, buoys, comms, _ = env.step(actions)
            
            # 计算总通信质量和干扰效果
            total_quality = sum([pair["quality"] for pair in comms])
            
            # 计算步奖励
            step_reward = 0
            for i in range(1, env.num_drones+1):
                reward = calculate_reward(env, prev_comm_quality, comms, i)
                step_reward += reward / env.num_drones
            
            episode_reward += step_reward
            
            print(f"Episode {episode}, Step {t}, Reward: {step_reward:.3f}, Total Comm Quality: {total_quality:.2f}")
            
            # 短暂暂停以便观察
            time.sleep(0.1)
        
        # 计算平均每步奖励
        avg_episode_reward = episode_reward / step_count if step_count > 0 else 0
        print(f"Episode {episode} completed, Average Reward: {avg_episode_reward:.3f}")

# 主函数
if __name__ == "__main__":
    # 创建环境
    env = Environment(buoy_json="my_buoys.json", num_drones=6, drone_speed=16.67)
    
    # 训练代理
    agent = train(env, max_episodes=1000, max_timesteps=250, update_timestep=1000, log_interval=10, save_interval=50)
    
    # 创建干扰模式选择器
    mode_selector = InterferenceModeSelector(env.interference_table)
    
    # 测试代理
    test(env, agent, mode_selector, num_episodes=5, max_timesteps=100)
