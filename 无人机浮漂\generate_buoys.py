import json
import random

def generate_buoy_positions(num_buoys=200, map_width=10000, map_height=10000):
    """
    随机生成浮漂位置
    """
    buoy_positions = []
    for i in range(num_buoys):
        x = round(random.uniform(0, map_width), 2)
        y = round(random.uniform(0, map_height), 2)
        buoy_positions.append({
            "id": i,
            "x": x,
            "y": y
        })
    return buoy_positions

def save_buoy_positions(buoy_positions, map_width, map_height, filename="buoy_positions.json"):
    data = {
        "map_width": map_width,
        "map_height": map_height,
        "num_buoys": len(buoy_positions),
        "buoy_positions": buoy_positions
    }
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    print(f"已生成 {len(buoy_positions)} 个浮漂，保存到 {filename}")

# 示例用法
if __name__ == "__main__":
    # 开发者可在这里自定义参数
    num_buoys = 200
    map_width = 10000
    map_height = 10000
    output_file = "my_buoys.json"

    buoys = generate_buoy_positions(num_buoys=num_buoys, map_width=map_width, map_height=map_height)
    save_buoy_positions(buoys, map_width, map_height, filename=output_file) 