# 无人机浮漂检测环境定义

# 环境尺寸参数
WORLD_WIDTH = 1000.0    # 世界宽度（米）
WORLD_HEIGHT = 1000.0   # 世界高度（米）
WORLD_DEPTH = 200.0     # 世界深度（米，最大飞行高度）

# 无人机参数
DRONE_MAX_SPEED = 20.0      # 最大飞行速度（米/秒）
DRONE_MAX_ANGULAR_SPEED = 1.0  # 最大角速度（弧度/秒）
DRONE_BATTERY_CAPACITY = 100.0  # 电池容量（百分比）
DRONE_BATTERY_CONSUMPTION = 0.1  # 每步电池消耗

# 浮漂参数
NUM_FLOATS = 10         # 浮漂数量
FLOAT_DETECTION_RANGE = 50.0  # 浮漂检测范围（米）
FLOAT_SIZE = 2.0        # 浮漂大小（米）

# 传感器参数
CAMERA_FOV = 60.0       # 相机视野角度（度）
CAMERA_RANGE = 100.0    # 相机检测范围（米）
CAMERA_RESOLUTION = (640, 480)  # 相机分辨率

# 仿真参数
MAX_STEPS = 1000        # 单局最大步数
RENDER = False          # 是否渲染
VIS_STEPS = 10          # 渲染频率
DISCRETE_ACTION = True  # 是否使用离散动作

# 动作空间定义
ACTION_SPACE_SIZE = {
    'move_forward_backward': 3,  # 前进/悬停/后退
    'move_left_right': 3,        # 左移/悬停/右移
    'move_up_down': 3,           # 上升/悬停/下降
    'rotate_yaw': 3,             # 左转/不转/右转
    'camera_pitch': 3,           # 相机上/平/下
    'camera_yaw': 3,             # 相机左/中/右
}

# 奖励参数
REWARDS = {
    'detect_float': 10.0,       # 检测到浮漂
    'high_accuracy': 5.0,       # 高精度检测
    'stable_flight': 1.0,       # 稳定飞行
    'battery_efficiency': 2.0,  # 电池使用效率
    'collision': -50.0,         # 碰撞惩罚
    'battery_empty': -30.0,     # 电池耗尽
    'detection_error': -5.0,    # 检测错误
    'unstable_flight': -2.0,    # 飞行不稳定
}

# 颜色定义（用于可视化）
COLORS = {
    'drone': (0, 0, 255),       # 蓝色
    'float': (255, 0, 0),       # 红色
    'detected_float': (0, 255, 0),  # 绿色
    'background': (255, 255, 255),   # 白色
    'obstacle': (128, 128, 128),     # 灰色
}
