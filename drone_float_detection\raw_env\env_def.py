# 无人机浮漂检测环境定义

# 环境尺寸参数 - 2D平面
WORLD_WIDTH = 1000.0    # 世界宽度（米）
WORLD_HEIGHT = 1000.0   # 世界高度（米）

# 无人机参数 - 简化为2D移动
DRONE_SPEED = 16.67     # 无人机移动速度（米/秒）

# 浮漂参数
NUM_FLOATS = 10         # 浮漂数量
FLOAT_SIZE = 2.0        # 浮漂大小（米）

# 干扰参数
INTERFERENCE_RANGE = 1000.0  # 干扰范围（米）

# 仿真参数
MAX_STEPS = 1000        # 单局最大步数
RENDER = False          # 是否渲染
VIS_STEPS = 10          # 渲染频率
DISCRETE_ACTION = True  # 是否使用离散动作

# 动作空间定义 - 2D平面移动
ACTION_SPACE_SIZE = 5  # 上、下、左、右、原地不动

# 动作映射
ACTIONS = {
    0: 'up',      # 上
    1: 'down',    # 下
    2: 'left',    # 左
    3: 'right',   # 右
    4: 'stay',    # 原地不动
}

# 奖励参数
REWARDS = {
    'interfere_float': 10.0,        # 干扰到新浮漂
    'continuous_interference': 1.0,  # 持续干扰奖励
    'out_of_bounds': -10.0,         # 出界惩罚
}

# 颜色定义（用于可视化）
COLORS = {
    'drone': (0, 0, 255),       # 蓝色
    'float': (255, 0, 0),       # 红色
    'detected_float': (0, 255, 0),  # 绿色
    'background': (255, 255, 255),   # 白色
    'obstacle': (128, 128, 128),     # 灰色
}
