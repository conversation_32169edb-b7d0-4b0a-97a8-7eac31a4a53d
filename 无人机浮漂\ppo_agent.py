import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.distributions import Categorical
import copy

# 定义注意力模块
class AttentionModule(nn.Module):
    def __init__(self, input_dim, hidden_dim):
        super(AttentionModule, self).__init__()
        self.query = nn.Linear(input_dim, hidden_dim)
        self.key = nn.Linear(input_dim, hidden_dim)
        self.value = nn.Linear(input_dim, hidden_dim)
        self.scale = hidden_dim ** 0.5
    
    def forward(self, x):
        # x shape: [batch_size, seq_len, input_dim]
        q = self.query(x)  # [batch_size, seq_len, hidden_dim]
        k = self.key(x)    # [batch_size, seq_len, hidden_dim]
        v = self.value(x)  # [batch_size, seq_len, hidden_dim]
        
        # 计算注意力分数
        attn = torch.bmm(q, k.transpose(1, 2)) / self.scale  # [batch_size, seq_len, seq_len]
        attn = F.softmax(attn, dim=2)
        
        # 应用注意力
        out = torch.bmm(attn, v)  # [batch_size, seq_len, hidden_dim]
        return out

# 定义策略网络
class PolicyNetwork(nn.Module):
    def __init__(self, state_dim, action_dim, hidden_dim=256):
        super(PolicyNetwork, self).__init__()
        
        # 基本特征提取器（无人机位置和边界距离）
        self.base_encoder = nn.Sequential(
            nn.Linear(6, hidden_dim // 4),
            nn.ReLU(),
            nn.Linear(hidden_dim // 4, hidden_dim // 4),
            nn.ReLU()
        )
        
        # 浮漂特征提取器
        self.buoy_encoder = nn.Sequential(
            nn.Linear(2, hidden_dim // 8),
            nn.ReLU()
        )
        
        # 通信对特征提取器
        self.comm_encoder = nn.Sequential(
            nn.Linear(6, hidden_dim // 4),
            nn.ReLU()
        )
        
        # 浮漂注意力模块
        self.buoy_attention = AttentionModule(hidden_dim // 8, hidden_dim // 8)
        
        # 通信对注意力模块
        self.comm_attention = AttentionModule(hidden_dim // 4, hidden_dim // 4)
        
        # 特征融合
        fusion_input_dim = hidden_dim // 4 + hidden_dim // 8 + hidden_dim // 4
        
        # 决策网络
        self.actor_net = nn.Sequential(
            nn.Linear(fusion_input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, action_dim),
            nn.Softmax(dim=-1)
        )
        
        # 价值网络
        self.critic_net = nn.Sequential(
            nn.Linear(fusion_input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1)
        )
        
    def forward(self, x):
        batch_size = x.size(0) if len(x.size()) > 1 else 1
        if len(x.size()) == 1:
            x = x.unsqueeze(0)
            
        # 分离不同特征组
        base_features = x[:, :6]  # 6个基本特征
        buoy_features = x[:, 6:26].view(batch_size, 10, 2)  # 10个浮漂，每个2个特征
        comm_features = x[:, 26:].view(batch_size, 5, 6)  # 5个通信对，每个6个特征
        
        # 编码基本特征
        base_encoded = self.base_encoder(base_features)
        
        # 编码浮漂特征
        buoy_encoded = self.buoy_encoder(buoy_features.reshape(-1, 2)).view(batch_size, 10, -1)
        buoy_attended = self.buoy_attention(buoy_encoded)
        buoy_features_pooled = torch.mean(buoy_attended, dim=1)  # [batch_size, hidden_dim//8]
        
        # 编码通信对特征
        comm_encoded = self.comm_encoder(comm_features.reshape(-1, 6)).view(batch_size, 5, -1)
        comm_attended = self.comm_attention(comm_encoded)
        comm_features_pooled = torch.mean(comm_attended, dim=1)  # [batch_size, hidden_dim//4]
        
        # 特征融合
        combined_features = torch.cat([base_encoded, buoy_features_pooled, comm_features_pooled], dim=1)
        
        # 决策和价值输出
        action_probs = self.actor_net(combined_features)
        state_value = self.critic_net(combined_features)
        
        return action_probs, state_value
    
    def act(self, state):
        action_probs, _ = self.forward(state)
        dist = Categorical(action_probs)
        action = dist.sample()
        return action.item(), dist.log_prob(action)
    
    def evaluate(self, state, action):
        action_probs, state_value = self.forward(state)
        dist = Categorical(action_probs)
        action_log_probs = dist.log_prob(action)
        entropy = dist.entropy()
        return action_log_probs, state_value, entropy

# PPO智能体
class PPOAgent:
    def __init__(self, state_dim, action_dim, lr=3e-4, gamma=0.99, eps_clip=0.2, K_epochs=10):
        self.gamma = gamma
        self.eps_clip = eps_clip
        self.K_epochs = K_epochs
        
        self.policy = PolicyNetwork(state_dim, action_dim)
        self.optimizer = optim.Adam(self.policy.parameters(), lr=lr)
        
        self.policy_old = copy.deepcopy(self.policy)
        
        self.MseLoss = nn.MSELoss()
        
    def select_action(self, state):
        with torch.no_grad():
            state = torch.FloatTensor(state)
            action, action_log_prob = self.policy_old.act(state)
        
        return action, action_log_prob
    
    def update(self, memory):
        # 蒙特卡洛估计回报
        rewards = []
        discounted_reward = 0
        for reward, is_terminal in zip(reversed(memory.rewards), reversed(memory.is_terminals)):
            if is_terminal:
                discounted_reward = 0
            discounted_reward = reward + (self.gamma * discounted_reward)
            rewards.insert(0, discounted_reward)
            
        # 标准化回报
        rewards = torch.tensor(rewards, dtype=torch.float32)
        rewards = (rewards - rewards.mean()) / (rewards.std() + 1e-5)
        
        # 转换为张量
        old_states = torch.FloatTensor(np.array(memory.states))
        old_actions = torch.tensor(memory.actions, dtype=torch.int64)
        old_log_probs = torch.tensor(memory.log_probs, dtype=torch.float32)
        
        # 优化策略K_epochs次
        for _ in range(self.K_epochs):
            # 评估旧动作和值
            log_probs, state_values, entropy = self.policy.evaluate(old_states, old_actions)
            
            # 计算比率（pi_theta / pi_theta__old）
            ratios = torch.exp(log_probs - old_log_probs.detach())
            
            # 计算替代损失（surrogate loss）
            advantages = rewards - state_values.detach().squeeze()
            surr1 = ratios * advantages
            surr2 = torch.clamp(ratios, 1-self.eps_clip, 1+self.eps_clip) * advantages
            
            # 最终损失包括策略损失和价值损失
            # 修复MSE损失警告：确保rewards和state_values具有相同的形状
            loss = -torch.min(surr1, surr2) + 0.5*self.MseLoss(state_values.squeeze(), rewards) - 0.01*entropy
            
            # 梯度下降
            self.optimizer.zero_grad()
            loss.mean().backward()
            self.optimizer.step()
            
        # 复制新的权重到旧策略
        self.policy_old.load_state_dict(self.policy.state_dict())

# 经验回放内存
class Memory:
    def __init__(self):
        self.actions = []
        self.states = []
        self.log_probs = []
        self.rewards = []
        self.is_terminals = []
    
    def clear_memory(self):
        del self.actions[:]
        del self.states[:]
        del self.log_probs[:]
        del self.rewards[:]
        del self.is_terminals[:]

# 干扰模式选择器
class InterferenceModeSelector:
    def __init__(self, interference_table):
        self.interference_table = interference_table
    
    def select_best_mode(self, drone_positions, buoy_positions, comm_pairs, interference_range):
        """
        为每个无人机选择最佳干扰模式
        
        参数:
        - drone_positions: 无人机位置字典 {无人机名称: (x, y)}
        - buoy_positions: 浮漂位置列表 [{"id": id, "x": x, "y": y}, ...]
        - comm_pairs: 通信对列表 [{"buoy1": id1, "buoy2": id2, "mode": mode, "quality": quality}, ...]
        - interference_range: 干扰范围
        
        返回:
        - best_modes: 每个无人机的最佳干扰模式字典 {无人机名称: 最佳模式}
        """
        best_modes = {}
        
        # 对每个无人机选择最佳干扰模式
        for drone_name, (dx, dy) in drone_positions.items():
            # 找出该无人机干扰范围内的通信对
            affected_pairs = []
            for pair in comm_pairs:
                buoy1 = next(b for b in buoy_positions if b["id"] == pair["buoy1"])
                buoy2 = next(b for b in buoy_positions if b["id"] == pair["buoy2"])
                
                # 计算无人机到两个浮漂的距离
                dist1 = ((dx - buoy1["x"]) ** 2 + (dy - buoy1["y"]) ** 2) ** 0.5
                dist2 = ((dx - buoy2["x"]) ** 2 + (dy - buoy2["y"]) ** 2) ** 0.5
                
                # 检查通信链路是否穿过干扰范围
                # 使用点到线段的最短距离公式
                x1, y1 = buoy1["x"], buoy1["y"]
                x2, y2 = buoy2["x"], buoy2["y"]
                
                # 计算向量
                line_vec = (x2 - x1, y2 - y1)
                drone_to_buoy1_vec = (dx - x1, dy - y1)
                
                # 计算线段长度的平方
                line_length_squared = line_vec[0]**2 + line_vec[1]**2
                
                # 如果线段长度为0（两个浮漂位置相同），直接计算点到点距离
                if line_length_squared == 0:
                    dist_to_link = ((dx - x1)**2 + (dy - y1)**2)**0.5
                else:
                    # 计算投影比例
                    t = max(0, min(1, (drone_to_buoy1_vec[0]*line_vec[0] + drone_to_buoy1_vec[1]*line_vec[1]) / line_length_squared))
                    
                    # 计算投影点
                    proj_x = x1 + t * line_vec[0]
                    proj_y = y1 + t * line_vec[1]
                    
                    # 计算距离
                    dist_to_link = ((dx - proj_x)**2 + (dy - proj_y)**2)**0.5
                
                # 如果至少一个浮漂在干扰范围内或通信链路穿过干扰范围
                if dist1 <= interference_range or dist2 <= interference_range or dist_to_link <= interference_range:
                    affected_pairs.append(pair)
            
            # 如果没有影响到任何通信对，默认使用模式1
            if not affected_pairs:
                best_modes[drone_name] = 1
                continue
            
            # 计算每个干扰模式的总干扰效果
            best_mode = 1
            max_interference = 0
            
            for mode in range(1, 11):  # 干扰模式1-10
                total_interference = 0
                for pair in affected_pairs:
                    comm_mode = pair["mode"]  # 通信模式 0-19
                    interference_effect = self.interference_table[mode-1][comm_mode]
                    total_interference += interference_effect
                
                if total_interference > max_interference:
                    max_interference = total_interference
                    best_mode = mode
            
            best_modes[drone_name] = best_mode
            
        return best_modes 