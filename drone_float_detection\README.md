# 无人机浮漂检测项目

基于drill框架的无人机浮漂检测强化学习训练项目。

## 项目结构

```
drone_float_detection/
├── README.md                    # 项目说明
├── requirements.txt             # 依赖包
├── local_test.py               # 本地测试脚本
├── pipeline.py                 # 数据处理管道
├── env_interface.py            # 环境接口
├── configs/                    # 配置文件目录
│   ├── __init__.py
│   ├── builder_config.py       # 构建器配置
│   ├── network_config.py       # 网络结构配置
│   └── training_config.py      # 训练参数配置
├── raw_env/                    # 原始环境目录
│   ├── __init__.py
│   ├── env_def.py             # 环境定义
│   ├── drone_env.py           # 无人机环境实现
│   └── float_detection.py     # 浮漂检测逻辑
└── models/                     # 模型存储目录
    └── drone_model/
```

## 环境特征

### 无人机状态
- 位置信息：3D坐标 (x, y, z)
- 姿态信息：欧拉角 (roll, pitch, yaw)
- 速度信息：线速度和角速度
- 电池电量：剩余电量百分比
- 传感器状态：相机角度、变焦等

### 浮漂信息
- 位置信息：检测到的浮漂位置
- 可见性：是否在视野范围内
- 类型信息：浮漂类型标识
- 检测置信度：检测结果的可信度

### 环境信息
- 风速风向：影响飞行稳定性
- 天气条件：能见度等
- 地形信息：障碍物分布

## 动作空间

### 飞行控制
- 前进/后退：控制无人机前后移动
- 左转/右转：控制无人机转向
- 上升/下降：控制无人机高度
- 悬停：保持当前位置

### 传感器控制
- 相机俯仰：调整相机上下角度
- 相机偏航：调整相机左右角度
- 变焦控制：调整相机焦距

## 奖励机制

### 正奖励
- 成功检测到浮漂：+10
- 检测精度高：+5
- 保持稳定飞行：+1
- 电量使用效率：+2

### 负奖励
- 碰撞障碍物：-50
- 电量耗尽：-30
- 检测失误：-5
- 飞行不稳定：-2

## 使用方法

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 本地测试：
```bash
python local_test.py
```

3. 训练模型：
```bash
# 具体训练命令待补充
```

## 配置说明

- `builder_config.py`: 配置环境、智能体、管道等
- `network_config.py`: 配置神经网络结构
- `training_config.py`: 配置训练超参数

## 注意事项

1. 确保drill框架已正确安装
2. 根据实际硬件调整环境参数
3. 训练前检查配置文件的一致性
