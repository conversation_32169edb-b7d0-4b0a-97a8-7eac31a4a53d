import numpy as np
import math
from typing import Dict, List, Tuple, Any
from .env_def import *


class DroneState:
    """无人机状态类 - 简化为2D"""
    def __init__(self):
        # 位置信息 (x, y) - 2D平面
        self.position = np.array([WORLD_WIDTH/2, WORLD_HEIGHT/2])

    def update_position(self, action: int):
        """根据动作更新位置"""
        if action == 0:  # 上
            self.position[1] += DRONE_SPEED
        elif action == 1:  # 下
            self.position[1] -= DRONE_SPEED
        elif action == 2:  # 左
            self.position[0] -= DRONE_SPEED
        elif action == 3:  # 右
            self.position[0] += DRONE_SPEED
        # action == 4 是原地不动，不需要更新位置

        # 确保无人机在边界内
        self.position[0] = np.clip(self.position[0], 0, WORLD_WIDTH)
        self.position[1] = np.clip(self.position[1], 0, WORLD_HEIGHT)

    def is_in_bounds(self) -> bool:
        """检查无人机是否在边界内"""
        return (0 <= self.position[0] <= WORLD_WIDTH and
                0 <= self.position[1] <= WORLD_HEIGHT)


class FloatObject:
    """浮漂对象类"""
    def __init__(self, position: np.ndarray, float_type: int = 0):
        self.position = position
        self.float_type = float_type
        self.interfered = False  # 是否被干扰

    def distance_to(self, drone_pos: np.ndarray) -> float:
        """计算到无人机的距离"""
        return np.linalg.norm(self.position[:2] - drone_pos)

    def is_in_interference_range(self, drone_pos: np.ndarray) -> bool:
        """检查是否在干扰范围内"""
        return self.distance_to(drone_pos) <= INTERFERENCE_RANGE


class DroneFloatEnv:
    """无人机浮漂检测环境"""

    def __init__(self):
        self.drone = DroneState()
        self.floats = []
        self.step_count = 0
        self.total_reward = 0.0
        self.interfered_floats = set()
        self._generate_floats()

    def _generate_floats(self):
        """生成随机分布的浮漂"""
        self.floats = []
        for i in range(NUM_FLOATS):
            # 随机生成浮漂位置（2D平面）
            x = np.random.uniform(0, WORLD_WIDTH)
            y = np.random.uniform(0, WORLD_HEIGHT)
            position = np.array([x, y])
            float_type = np.random.randint(0, 3)  # 3种浮漂类型
            self.floats.append(FloatObject(position, float_type))

    def reset(self) -> Dict[str, Any]:
        """重置环境"""
        self.drone = DroneState()
        self.step_count = 0
        self.total_reward = 0.0
        self.interfered_floats = set()
        self._generate_floats()
        return self._get_observation()

    def step(self, action: int) -> Tuple[Dict[str, Any], float, bool, Dict[str, Any]]:
        """执行一步动作"""
        # 执行动作
        self.drone.update_position(action)

        # 计算奖励
        reward = self._calculate_reward()
        self.total_reward += reward

        # 检查是否结束
        done = self._is_done()

        # 更新步数
        self.step_count += 1

        # 获取观测
        obs = self._get_observation()

        # 额外信息
        info = {
            'step_count': self.step_count,
            'total_reward': self.total_reward,
            'interfered_floats': len(self.interfered_floats),
        }

        return obs, reward, done, info

    def _calculate_reward(self) -> float:
        """计算奖励 - 基于干扰效果"""
        reward = 0.0

        # 检查哪些浮漂在干扰范围内
        current_interfered = set()
        for i, float_obj in enumerate(self.floats):
            if float_obj.is_in_interference_range(self.drone.position):
                current_interfered.add(i)
                float_obj.interfered = True

                # 新干扰的浮漂给予奖励
                if i not in self.interfered_floats:
                    reward += REWARDS['interfere_float']
                    self.interfered_floats.add(i)

        # 持续干扰奖励
        reward += len(current_interfered) * REWARDS['continuous_interference']

        # 边界惩罚
        if not self.drone.is_in_bounds():
            reward += REWARDS['out_of_bounds']

        return reward


    def _get_observation(self) -> Dict[str, Any]:
        """获取观测信息 - 简化为2D"""
        # 无人机状态
        drone_obs = {
            'position': self.drone.position.copy(),
        }

        # 浮漂信息
        float_info = []
        for float_obj in self.floats:
            info = {
                'position': float_obj.position.copy(),
                'type': float_obj.float_type,
                'interfered': float_obj.interfered,
                'distance': float_obj.distance_to(self.drone.position),
            }
            float_info.append(info)

        # 环境信息
        env_info = {
            'step_count': self.step_count,
            'total_floats': len(self.floats),
            'interfered_count': len(self.interfered_floats),
        }

        return {
            'drone': drone_obs,
            'floats': float_info,
            'env': env_info,
        }

    def _is_done(self) -> bool:
        """检查是否结束"""
        # 达到最大步数
        if self.step_count >= MAX_STEPS:
            return True

        # 干扰到所有浮漂
        if len(self.interfered_floats) >= len(self.floats):
            return True

        return False


def make_drone_env():
    """创建无人机浮漂检测环境"""
    return DroneFloatEnv()
