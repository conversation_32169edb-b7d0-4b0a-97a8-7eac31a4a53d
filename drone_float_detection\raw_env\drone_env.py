import numpy as np
import math
from typing import Dict, List, Tuple, Any
from .env_def import *


class DroneState:
    """无人机状态类"""
    def __init__(self):
        # 位置信息 (x, y, z)
        self.position = np.array([WORLD_WIDTH/2, WORLD_HEIGHT/2, 50.0])
        # 姿态信息 (roll, pitch, yaw)
        self.orientation = np.array([0.0, 0.0, 0.0])
        # 线速度 (vx, vy, vz)
        self.velocity = np.array([0.0, 0.0, 0.0])
        # 角速度 (wx, wy, wz)
        self.angular_velocity = np.array([0.0, 0.0, 0.0])
        # 电池电量
        self.battery = DRONE_BATTERY_CAPACITY
        # 相机角度 (pitch, yaw)
        self.camera_angle = np.array([0.0, 0.0])

    def update(self, dt: float = 1.0):
        """更新无人机状态"""
        # 更新位置
        self.position += self.velocity * dt
        # 更新姿态
        self.orientation += self.angular_velocity * dt
        # 限制姿态角度在合理范围内
        self.orientation = np.clip(self.orientation, -np.pi, np.pi)
        # 消耗电池
        self.battery -= DRONE_BATTERY_CONSUMPTION
        self.battery = max(0.0, self.battery)

    def is_in_bounds(self) -> bool:
        """检查无人机是否在边界内"""
        return (0 <= self.position[0] <= WORLD_WIDTH and
                0 <= self.position[1] <= WORLD_HEIGHT and
                0 <= self.position[2] <= WORLD_DEPTH)

    def is_battery_empty(self) -> bool:
        """检查电池是否耗尽"""
        return self.battery <= 0.0


class FloatObject:
    """浮漂对象类"""
    def __init__(self, position: np.ndarray, float_type: int = 0):
        self.position = position
        self.float_type = float_type
        self.detected = False
        self.detection_count = 0

    def distance_to(self, drone_pos: np.ndarray) -> float:
        """计算到无人机的距离"""
        return np.linalg.norm(self.position - drone_pos)

    def is_in_detection_range(self, drone_pos: np.ndarray) -> bool:
        """检查是否在检测范围内"""
        return self.distance_to(drone_pos) <= FLOAT_DETECTION_RANGE


class DroneFloatEnv:
    """无人机浮漂检测环境"""

    def __init__(self):
        self.drone = DroneState()
        self.floats = []
        self.step_count = 0
        self.total_reward = 0.0
        self.detected_floats = set()
        self._generate_floats()

    def _generate_floats(self):
        """生成随机分布的浮漂"""
        self.floats = []
        for i in range(NUM_FLOATS):
            # 随机生成浮漂位置（在水面上，z=0）
            x = np.random.uniform(0, WORLD_WIDTH)
            y = np.random.uniform(0, WORLD_HEIGHT)
            z = 0.0  # 浮漂在水面上
            position = np.array([x, y, z])
            float_type = np.random.randint(0, 3)  # 3种浮漂类型
            self.floats.append(FloatObject(position, float_type))

    def reset(self) -> Dict[str, Any]:
        """重置环境"""
        self.drone = DroneState()
        self.step_count = 0
        self.total_reward = 0.0
        self.detected_floats = set()
        self._generate_floats()
        return self._get_observation()

    def step(self, actions: Dict[str, int]) -> Tuple[Dict[str, Any], float, bool, Dict[str, Any]]:
        """执行一步动作"""
        # 执行动作
        self._execute_actions(actions)

        # 更新无人机状态
        self.drone.update()

        # 计算奖励
        reward = self._calculate_reward()
        self.total_reward += reward

        # 检查是否结束
        done = self._is_done()

        # 更新步数
        self.step_count += 1

        # 获取观测
        obs = self._get_observation()

        # 额外信息
        info = {
            'step_count': self.step_count,
            'total_reward': self.total_reward,
            'detected_floats': len(self.detected_floats),
            'battery': self.drone.battery,
        }

        return obs, reward, done, info

    def _execute_actions(self, actions: Dict[str, int]):
        """执行动作"""
        # 前后移动
        if actions.get('move_forward_backward', 1) == 0:  # 前进
            self.drone.velocity[0] = DRONE_MAX_SPEED * math.cos(self.drone.orientation[2])
            self.drone.velocity[1] = DRONE_MAX_SPEED * math.sin(self.drone.orientation[2])
        elif actions.get('move_forward_backward', 1) == 2:  # 后退
            self.drone.velocity[0] = -DRONE_MAX_SPEED * math.cos(self.drone.orientation[2])
            self.drone.velocity[1] = -DRONE_MAX_SPEED * math.sin(self.drone.orientation[2])
        else:  # 悬停
            self.drone.velocity[0] = 0.0
            self.drone.velocity[1] = 0.0

        # 左右移动
        if actions.get('move_left_right', 1) == 0:  # 左移
            self.drone.velocity[0] += -DRONE_MAX_SPEED * math.sin(self.drone.orientation[2])
            self.drone.velocity[1] += DRONE_MAX_SPEED * math.cos(self.drone.orientation[2])
        elif actions.get('move_left_right', 1) == 2:  # 右移
            self.drone.velocity[0] += DRONE_MAX_SPEED * math.sin(self.drone.orientation[2])
            self.drone.velocity[1] += -DRONE_MAX_SPEED * math.cos(self.drone.orientation[2])

        # 上下移动
        if actions.get('move_up_down', 1) == 0:  # 上升
            self.drone.velocity[2] = DRONE_MAX_SPEED
        elif actions.get('move_up_down', 1) == 2:  # 下降
            self.drone.velocity[2] = -DRONE_MAX_SPEED
        else:  # 悬停
            self.drone.velocity[2] = 0.0

        # 偏航旋转
        if actions.get('rotate_yaw', 1) == 0:  # 左转
            self.drone.angular_velocity[2] = DRONE_MAX_ANGULAR_SPEED
        elif actions.get('rotate_yaw', 1) == 2:  # 右转
            self.drone.angular_velocity[2] = -DRONE_MAX_ANGULAR_SPEED
        else:  # 不转
            self.drone.angular_velocity[2] = 0.0

        # 相机俯仰
        if actions.get('camera_pitch', 1) == 0:  # 向上
            self.drone.camera_angle[0] = min(self.drone.camera_angle[0] + 0.1, np.pi/2)
        elif actions.get('camera_pitch', 1) == 2:  # 向下
            self.drone.camera_angle[0] = max(self.drone.camera_angle[0] - 0.1, -np.pi/2)

        # 相机偏航
        if actions.get('camera_yaw', 1) == 0:  # 向左
            self.drone.camera_angle[1] = min(self.drone.camera_angle[1] + 0.1, np.pi/2)
        elif actions.get('camera_yaw', 1) == 2:  # 向右
            self.drone.camera_angle[1] = max(self.drone.camera_angle[1] - 0.1, -np.pi/2)

    def _calculate_reward(self) -> float:
        """计算奖励"""
        reward = 0.0

        # 检测浮漂奖励
        for i, float_obj in enumerate(self.floats):
            if float_obj.is_in_detection_range(self.drone.position):
                if i not in self.detected_floats:
                    reward += REWARDS['detect_float']
                    self.detected_floats.add(i)
                    float_obj.detected = True

                # 高精度检测奖励（距离越近奖励越高）
                distance = float_obj.distance_to(self.drone.position)
                accuracy_bonus = REWARDS['high_accuracy'] * (1.0 - distance / FLOAT_DETECTION_RANGE)
                reward += accuracy_bonus

        # 稳定飞行奖励
        if np.linalg.norm(self.drone.velocity) < DRONE_MAX_SPEED * 0.5:
            reward += REWARDS['stable_flight']

        # 电池使用效率奖励
        if self.drone.battery > 50.0:
            reward += REWARDS['battery_efficiency']

        # 边界碰撞惩罚
        if not self.drone.is_in_bounds():
            reward += REWARDS['collision']

        # 电池耗尽惩罚
        if self.drone.is_battery_empty():
            reward += REWARDS['battery_empty']

        # 飞行不稳定惩罚
        if np.linalg.norm(self.drone.velocity) > DRONE_MAX_SPEED * 0.8:
            reward += REWARDS['unstable_flight']

        return reward

    def _get_observation(self) -> Dict[str, Any]:
        """获取观测信息"""
        # 无人机状态
        drone_obs = {
            'position': self.drone.position.copy(),
            'orientation': self.drone.orientation.copy(),
            'velocity': self.drone.velocity.copy(),
            'angular_velocity': self.drone.angular_velocity.copy(),
            'battery': self.drone.battery,
            'camera_angle': self.drone.camera_angle.copy(),
        }

        # 浮漂信息（只包含检测范围内的浮漂）
        visible_floats = []
        for float_obj in self.floats:
            if float_obj.is_in_detection_range(self.drone.position):
                float_info = {
                    'position': float_obj.position.copy(),
                    'type': float_obj.float_type,
                    'detected': float_obj.detected,
                    'distance': float_obj.distance_to(self.drone.position),
                }
                visible_floats.append(float_info)

        # 环境信息
        env_info = {
            'step_count': self.step_count,
            'total_floats': len(self.floats),
            'detected_count': len(self.detected_floats),
        }

        return {
            'drone': drone_obs,
            'floats': visible_floats,
            'env': env_info,
        }

    def _is_done(self) -> bool:
        """检查是否结束"""
        # 达到最大步数
        if self.step_count >= MAX_STEPS:
            return True

        # 电池耗尽
        if self.drone.is_battery_empty():
            return True

        # 出界
        if not self.drone.is_in_bounds():
            return True

        # 检测到所有浮漂
        if len(self.detected_floats) >= len(self.floats):
            return True

        return False


def make_drone_env():
    """创建无人机浮漂检测环境"""
    return DroneFloatEnv()
