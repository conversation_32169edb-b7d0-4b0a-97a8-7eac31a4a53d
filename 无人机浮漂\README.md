# 无人机浮漂干扰系统

这是一个使用PPO强化学习算法控制无人机干扰浮漂通信的系统。

## 项目结构

- `environment.py`: 环境模拟器，包含无人机、浮漂和通信模型
- `generate_buoys.py`: 生成随机浮漂位置
- `my_buoys.json`: 浮漂位置数据
- `visualization.py`: 基础可视化工具
- `ppo_agent.py`: PPO强化学习代理实现
- `train_ppo.py`: 训练PPO代理的主程序
- `visualization_with_ppo.py`: 使用PPO代理控制无人机的可视化工具

## 系统说明

系统模拟了多个无人机在海上干扰浮漂之间通信的场景。无人机可以移动（上、下、左、右、原地不动）并选择不同的干扰模式（1-10）来干扰浮漂之间的通信。

### 干扰模式与干扰范围

系统中有10种干扰模式，对应20种通信模式有不同的干扰效果，这些效果存储在`interference_table`中。干扰模式选择器会根据当前环境状态为每个无人机选择最佳的干扰模式。

无人机可以通过两种方式干扰通信：
1. 当浮漂在无人机干扰范围内
2. 当通信链路（两个浮漂连线）穿过无人机干扰范围

这两种情况下，无人机都会对通信产生干扰效果。系统使用点到线段的最短距离来判断通信链路是否穿过干扰范围。

### PPO强化学习

系统使用PPO（Proximal Policy Optimization）算法训练无人机控制策略，使无人机能够学会如何移动以最大化干扰效果。

## 使用方法

### 1. 训练PPO代理

```bash
python train_ppo.py
```

训练完成后，模型将保存在`models`目录下。

### 2. 可视化PPO控制的无人机

```bash
python visualization_with_ppo.py
```

这将启动可视化界面，展示PPO控制的无人机如何在环境中移动和干扰浮漂通信。
- 蓝色点：浮漂
- 红色星形：无人机
- 红色虚线圆：无人机干扰范围
- 彩色实线：正常通信链路
- 红色虚线：受干扰的通信链路

### 3. 生成新的浮漂位置（可选）

```bash
python generate_buoys.py
```

## 状态表示

对于每个无人机，我们提取以下特征作为状态：

1. 无人机自身位置 (x, y)
2. 无人机到地图边界的距离 (4个值)
3. 附近浮漂的相对位置 (最近10个浮漂，每个2个值)
4. 附近通信对的信息 (最近5个通信对，每个6个值：浮漂1相对位置，浮漂2相对位置，通信质量，通信模式)

## 奖励函数

奖励函数由以下几部分组成：

1. 通信质量变化奖励：通信质量下降越多，奖励越高
2. 干扰效果奖励：无人机产生的干扰效果越大，奖励越高
3. 覆盖通信对数量奖励：无人机覆盖的通信对越多，奖励越高 