from drill.feature import VectorFeature, PlainFeature, OnehotFeature, RangedFeature
from drill.feature import CommonFeatureSet, EntityFeatureSet
from drill.model.tf.network.encoder import EntityEncoder, CommonEncoder, encoder_pooling
from drill.model.tf.network.aggregator import GRUAggregator, DenseAggregator, LSTMAggregator
from drill.model.tf.network.decoder import CategoricalDecoder, SingleSelectiveDecoder, UnorderedMultiSelectiveDecoder, \
    OrderedMultipleSelectiveDecoder, GaussianDecoder
from drill.model.tf.network.layer import ValueApproximator
from drill.model.tf.network.commander import CommanderNetwork
from drill.model.tf import CommanderModelPPO



# -------------------------特征配置部分-------------------------
# 根据Agent属性采用不同类型的feature，在整体封装为EntityFeatureSet
entity_feature_set = EntityFeatureSet(              
    name='my_units',                             # 特征名称
    max_length=4,                                # 需要控制的agent数量
    feature_dict={                               # 定义单个agent的属性与特征类型间的关系
        "pos": VectorFeature(3),                 # 坐标 --> 向量特征类型   
        'theta': PlainFeature(),                 # 转角 --> 单数据特征类型    
        'v': PlainFeature(),                     # 速度 --> 单数据特征类型 
        'alpha_max': PlainFeature(),             # alpha转角限制 --> 单数据特征类型
        'theta_max': PlainFeature(),             # theta转角限制 --> 单数据特征类型
        'dv': PlainFeature(),                    # 垂直（深度）速度 --> 单数据特征类型
        'radar': VectorFeature(3),               # 雷达最远点坐标信息 --> 向量特征类型
    },
)
# 将目标特性封装为CommonFeatureSet
target_feature_set = CommonFeatureSet(
    name='b_info',
    feature_dict={
        'b_pos': VectorFeature(3),               # 目标坐标信息 --> 向量特征类型
        'b_visible': PlainFeature(),             # 目标是否可见 --> 单数据特征类型
    }
)

# -------------------------编码器配置 部分-------------------------
# 编码器的作用是将特征转换为向量，以便于后续的聚合器和解码器使用。
# EntityEncoder可以采用不同的模型，如Transformer、LSTM等。
# EntityEncoder可以采用不同的池化方法，如Max、Attention等。
# 编码器可以采用不同的隐藏层大小，如[256, 128]、[512, 256]等。
# 编码器可以采用不同的池化方法，如Max、Attention等。
encoders = {
    "entity_encoder": {                          # 对实体特征进行编码
        "class": EntityEncoder,                  # 选择编码器类型 ---> 实体编码类
        "params": {                              # 设定实体编码类的参数
            "hidden_layer_sizes": [256, 128],    # 隐藏层参数 ---> [256, 128]
            "transformer": None,                 # transformer参数 ---> None
            "pooling": encoder_pooling.Max(),    # 池化方法 ---> Max()        
        },
        "inputs": entity_feature_set             # 实体编码类接受的数据格式 ---> entity_feature_set
    },
    "common_encoder": {                          # 对目标特征进行编码
        "class": CommonEncoder,                  # 选择编码器类型 ---> 通用编码器类
        "params": {
            "hidden_layer_sizes": [256, 128],    # 隐藏层参数 ---> [256, 128]
        },
        "inputs": target_feature_set             # 通用编码类接受的数据格式 ---> target_feature_set
    }
}


# 聚合器的作用是将编码器输出的向量聚合为单个向量，以便于后续的解码器使用。
# 聚合器可以采用不同的模型，如GRU、LSTM、Dense等。
# 聚合器可以采用不同的隐藏层大小，如[256, 128]、[512, 256]等。


# -------------------------聚合器部分-------------------------
aggregator = {
    "class": GRUAggregator,                      # 选择聚合器类型 ---> GRUAggregator
    "params": {                                  # 设定GRUAggregator类的参数
        "hidden_layer_sizes": [512, 256],        # 隐藏层参数 ---> [512, 256]
        "state_size": 64,                        # 状态大小 ---> 64
        "output_size": 512,                      # 输出大小 ---> 512
        "seq_len": 1,                            # 序列长度 ---> 1
    }
}


# 解码器的作用是将聚合器输出的向量解码为动作，以便于后续的执行器使用。
# 解码器可以采用不同的模型，如CategoricalDecoder、GaussianDecoder等。
# 解码器可以采用不同的隐藏层大小，如[256, 128]、[512, 256]等。


# -------------------------解码器部分-------------------------
decoders = {
    "action_x": {                               # agent可执行动作:顺时针训练角度 
        "class": CategoricalDecoder,            # 动作类型 ---> 离散动作选择
        "params": {
            "n": 10,                            # 动作数量 ---> 10
            "hidden_layer_sizes": [512, 256],   # 隐藏层参数 ---> [512, 256]
        },
    },
    "action_y": {                               # agent可执行动作:水平速度 
        "class": CategoricalDecoder,            # 动作类型 ---> 离散动作选择
        "params": {
            "n": 10,                            # 动作数量 ---> 10
            "hidden_layer_sizes": [512, 256],   # 隐藏层参数 ---> [512, 256]
        }
    },
    "action_dv": {                              # agent可执行动作:垂直速度 
        "class": CategoricalDecoder,            # 动作类型 ---> 离散动作选择
        "params": {
            "n": 10,                            # 动作数量 ---> 10
            "hidden_layer_sizes": [512, 256],   # 隐藏层参数 ---> [512, 256]
        }
    }
}



# -------------------------价值网络部分-------------------------
value_layer = {
    "class": ValueApproximator,                # 选择价值评估网络类型 ---> ValueApproximator
    "params": {
        "hidden_layer_sizes": [64, 32],        # 隐藏层参数 ---> [64, 32]
    }
}

# ------------ 组合 编码器、聚合器、解码器、价值网络-----------------
network = {
    "class": CommanderNetwork,                      # 选择网络类型 --> CommanderNetwork  
    "params": {
        "encoder_config": encoders,                 # 编码器 --> encoders
        "aggregator_config": aggregator,            # 聚合器 --> aggregator
        "decoder_config": decoders,                 # 解码器 --> decoders
        "value_approximator_config": value_layer,   # 价值网络 --> value_layer
    }
}

# -------------------------模型定义部分-------------------------
model_config = {
    "f4v1_model": {
        "class": CommanderModelPPO,                 # 选用PPO算法 --> CommanderModelPPO
        "params": {
            "network": network,                     # 神经网络结构 --> network
            "learning_rate": 2e-4,                  # 学习率 --> 2e-4
            "clip_param": 0.3,                      # ppo actor的clip参数 --> 0.3
            "vf_clip_param": 10.,                   # ppo critic的clip参数 --> 10
            "vf_loss_coef": 1.,                     # value loss 的 缩放因子 --> 1
            "entropy_coef": 0.1,                    # entropy loss 的 缩放因子 --> 0.1
        },
        "save": {
            "interval": 100,                        # 模型存储间隔 --> 100
        },
    },
}



















from drill.builder import BPBuilder
from env_interface import F4v1Env
from drill.pipeline.agent_pipeline import AgentPipeline, HandlerSpecies
from drill.pipeline import GlobalPipeline
from pipeline import feature_handler, reward_handler, action_handler, player_done_process
# 导入了我方特征 敌方特征 和 model配置
from configs.network_config import entity_feature_set, target_feature_set
from configs.network_config import model_config

 # 导入训练环境类 ---> F4v1Env          
env = {"class": F4v1Env, "params": {}}              
# 智能体名称
AGENT_NAMES = ['player0', 'player1', 'player2', 'player3']       
# 根据pipeline.py中的feature_handler，action_handler，reward_handler配置pipeline
pipeline = {
    "f4v1_pipeline": {                                  # 设定pipeline名称
        "class": AgentPipeline,                         # 选择pipeline类型 ---> AgentPipeline
        "params": {
            "handler_dict": {
                HandlerSpecies.FEATURE: (
                    feature_handler,                    # 特征函数  
                    [
                        entity_feature_set,             # 特征函数包含的实体特征
                        target_feature_set,             # 特征函数包含目标特征 
                        # x_mask_feature_set,           # 动作掩码特征
                    ]
                    ),
                HandlerSpecies.REWARD: reward_handler,  # 奖励函数 ---> reward_handler
                HandlerSpecies.ACTION: action_handler,  # 动作函数 ---> action_handler
            },
            "batch_config": {
                "gamma": 0.99,                          # 折扣因子 ---> 0.99
                "lamb": 0.95,                           # 衰减因子 ---> 0.95
            }
        },
    },
    "global": {
        "class": GlobalPipeline,
        "params": {
            "pre_process": player_done_process         # 过滤死亡单位数据 
        }
    }
}
# 智能体与神经网络模型映射关系，支持多智能体
agents = {
    'player0': {
        # 选择智能体对应网络模型 ---> 与network_config.py中model_config的键值对应
        "model": "f4v1_model",                         
        # 选择对应pipeline配置 ---> 与 上文 pipeline 的键值对应
        "pipeline": "f4v1_pipeline"                    
    },
    'player1': {
        "model": "f4v1_model",
        "pipeline": "f4v1_pipeline"
    },
    'player2': {
        "model": "f4v1_model",
        "pipeline": "f4v1_pipeline"
    },
    'player3': {
        "model": "f4v1_model",
        "pipeline": "f4v1_pipeline"
    },
}
# 构建训练环境
builder = BPBuilder(agents, model_config, env, pipeline)











































from drill.flow.flow_env_ppo import FlowEnvPPO
from drill.flow.flow_model_ppo import FlowModelPPO
from configs.builder_config import builder

# 从0开始构建训练 本地到云端 开始训练 的流程
# 现有个出版 pipe line等参数的配置
# 基于drill接口 接入别的算法
# 暂时基于F4V1 和carbort 功能
# docker镜像仓库


# -------------------------构建flow_config-------------------------
# 选择算法
algo = {'flow_env': FlowEnvPPO, 'flow_model': FlowModelPPO} 
flow_config = {
    'algorithm': algo,                              # 选择算法
    'builder': builder,                             # 配置builder_config实现的builder
    'actor_config': {                               # 配置actor
        'actor_0': {                                # 配置actor_0 ---> 训练actor
            # 配置需要训练的模型，多个模型通过列表方式导入   训练时的超参数
            'training_models': [                    
                {
                    'model_name': 'f4v1_model',     # 模型名称，注意跟network_config中对应
                    'fragment_size': 1024,          # GAE的切片大小，n-steps模式；若使用纯episode模式，需保证该值超过单局步长
                    'replay_size': 1,               # 若存在lstm、gru等时序网络，该数值可适度调大，默认1即可
                    'sample_batch_size': 128,       # 训练模型的batch_size，通常推荐为128/256/512
                    # 'max_data_reuse': 1,          # 在收集到下一个batch的数据前，最多对当前batch额外重复训练多少次, 设置为0即禁止重复使用
                    # 'putback_replays': False,     # 是否将使用的replay原路放回ReplayBuffer
                    # 'sample_mode': "LIFO",        # LIFO: 后进先出, RANDOM: 优先选择最新的未曾使用过的样本
                    # 'replay_buffer_size': 16,     # buffer中的batch_size数量上限，LIFO模式默认为16，RANDOM模式下默认为64。
                },
            ],
            'inference_models': None,               # 配置只跑前向的模型，inference_models列表中的模型仅进行模型推理，不参与训练
            # 若episode_mode为True，计算gae逻辑由nsteps（fragment_size）模式转为episode模式；需要注意的是，若使用episode模式，切片replay_size存在丢弃数据可能性。
            'episode_mode': False,                  # 是否使用episode模式
            'env_num': 20,                          # 仿真环境数量
            # extra_info中增加actor描述，extra_info支持字典格式对描述信息进行定义，可通过区分extra_info来实现"训练-验证"相独立
            'extra_info': {'index': 'training', 'description': 'used for training'},
        },
        'actor_1': {
            'training_models': None,                # 'training_models'为空，则该actor只跑前向
            'inference_models': ['f4v1_model'],     # 用来跑前向的模型 必须和model_config中的key完全一样！！
            'env_num': 5,                           # 仿真环境数量
            'extra_info': {'index': 'evaluating'},  # 仿真环境描述
        },
    },
}



"""
config文件中name必须保持一致的地方:

1.model_config、agents["model"]、training_models["model_name"]、inference_models保持一致
2.pipeline、agents["pipeline"]保持一致



"""

















from drill.pipeline.interface import ObsData, ActionData, History


def reward_handler(data: ObsData, history: History):
    # 在f4v1样例中，环境返回值包括了原始奖励，则在奖励计算时，可以直接使用环境奖励
    extra_info_dict = data.extra_info_dict
    # 如果extra_info_dict为空或不存在，则返回0；否则返回extra_info_dict['reward']
    if (extra_info_dict is None) or ('reward' not in extra_info_dict) or (not extra_info_dict):
        return {'reward': 0}
    else:
        return {'reward': extra_info_dict['reward']}




def feature_handler(data: ObsData, history: History):
    my_units_list, ally_feature = [], []
    for k, v in data.obs.items():   
        if k == 'b_info':                                      # 目标信息
            b_info = {
                'b_pos': v['b_pos'],
                'b_visible': v['b_visible']
            }
            continue
        if k[-1] == data.agent_name[-1]:                       # 自身特征在前 队友特征在后
            my_units_list.append(v)                            # 自身特征
        else:
            if any([item.sum() for item in v.values()]):       # 只考虑仍然存活的单位
                ally_feature.append(v)                         # 队友特征
    my_units_list.extend(ally_feature)              
    name2feature = {
        'my_units': my_units_list,                             # 自身特征 ---> my_units_list
        'b_info': b_info,                                      # 目标信息 ---> b_info
    }
    # 如果想将obs数据传到action_handler中，可通过该方式实现
    # history.agents_history[data.agent_name] = name2feature 
    return name2feature


def action_handler(data: ActionData, history: History) -> ActionData:
    # f4v1样例中，环境可以直接接受神经网络输出值，则不需要做动作映射处理
    # 如果想令某个动作头不参与训练，即valid_action为false，则可以通过该方式实现
    # data.action.pop('action_x')   
    return data


def player_done_process(data: ObsData, history: History):
    pre_agent_dict = {}                              # 存储存活单位的信息
    for agent_name, info in data.items():              
        # 对齐环境中单位的名字  将playerX ---->  player_X  因为agent_name和info.obs内的名字的不一致
        agent_name_fix = agent_name[:-1] + '_' + agent_name[-1]
        # 本环境中，死亡单位会传递全0的状态，我们可以通过判断状态是否全0来判断单位是否死亡
        player_done = not any([item.sum() for item in info.obs[agent_name_fix].values()])
        # episode没结束时，屏蔽死亡训练单位的数据
        if info.extra_info_dict.get("episode_done", False) or not player_done:     
            pre_agent_dict[agent_name] = info
    return pre_agent_dict, history
