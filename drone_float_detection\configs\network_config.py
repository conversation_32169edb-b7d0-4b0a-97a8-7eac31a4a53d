from drill.feature import VectorFeature, PlainFeature, OnehotFeature, RangedFeature
from drill.feature import CommonFeatureSet, EntityFeatureSet
from drill.model.tf.network.encoder import EntityEncoder, CommonEncoder, encoder_pooling
from drill.model.tf.network.aggregator import GRUAggregator, DenseAggregator, LSTMAggregator
from drill.model.tf.network.decoder import CategoricalDecoder, SingleSelectiveDecoder, UnorderedMultiSelectiveDecoder, \
    OrderedMultipleSelectiveDecoder, GaussianDecoder
from drill.model.tf.network.layer import ValueApproximator

from drill.model.tf import CommanderModelPPO

"""
常用特征模板
1.common_feature_set = CommonFeatureSet(name: str, feature_dict: dict)
    通用特征模版。作为相应数据类型的载体，用于提取通用特征。
    通用特征的特点是数据定长，数据输入的 shape 固定。

2.EntityFeatureSet(name: str, max_length: int, feature_dict: dict)
    实体特征模版。用于提取实体特征信息。
    实体特征不同于通用特征，其长度可变。在推演中由于毁伤或不完全观测等原因，导致实体信息形状不固定；
    而本特征模版则用于处理这类的变长信息。

常用特征
1.PlainFeature：
    单数据特征类型。用于表示单个连续的数据。
    例如: 一些经度、纬度、高度、血量、距离、都可以用此种特征表示。
2.VectorFeature(length：int)：
    向量特征类型。用于处理特征信息。在推演运行时，本类定义的特征类型支持向量输入，输入向量长度需要和初始化参数 length 相同
    例如：高纬的坐标信息。
3.RangedFeature(low: float, high: float, length: int)
    用于处理需要归一化的特征信息。在推演运行时，本类定义的特征类型支持单数据和向量类型输入。输入后会统一对数据做归一化处理。
    归一化处理方式是将每一个数据 x 都进行（x-最小值）/（最大值-最小值）的处理
4.OnehotFeature(depth: int)
    用于处理类别信息，可以将一个整数转换为一个 one-hot 的表示向量。
    例如：男女性别、存活状态。
"""

# -------------------------特征配置部分-------------------------
# 无人机状态特征
drone_feature_set = CommonFeatureSet(
    name='drone_state',
    feature_dict={
        "position": VectorFeature(2),  # 2D位置信息
        "step_count": PlainFeature(),  # 步数
        "interference_progress": PlainFeature(),  # 干扰进度
    }
)

# 浮漂特征
float_feature_set = EntityFeatureSet(
    name='floats',
    max_length=10,  # 最大浮漂数量，减少到10个
    feature_dict={
        "position": VectorFeature(2),  # 浮漂位置
        "type": OnehotFeature(3),      # 浮漂类型（3种）
        "distance": PlainFeature(),    # 距离
        "interfered": OnehotFeature(2), # 是否被干扰
    }
)

# -------------------------网络结构配置部分-------------------------
# 编码器配置
encoders = {
    "drone_encoder": {
        # 无人机状态编码器
        "class": CommonEncoder,
        "params": {
            "hidden_layer_sizes": [64, 32],
        },
        "inputs": drone_feature_set  # 输入无人机特征
    },
    "float_encoder": {
        # 浮漂编码器
        "class": EntityEncoder,
        "params": {
            "hidden_layer_sizes": [128, 64],
            "transformer": None,
            # 池化方法，使用最大池化
            "pooling": encoder_pooling.Max(),
        },
        "inputs": float_feature_set  # 输入浮漂特征
    }
}

# 聚合器配置
aggregator = {
    "class": DenseAggregator,
    "params": {
        "hidden_layer_sizes": [256, 128],
        "output_size": 256,
    }
}

# 解码器配置
decoders = {
    "action_decoder": {
        "class": CategoricalDecoder,
        "params": {
            "n": 5,  # 5个动作：上、下、左、右、原地不动
            "hidden_layer_sizes": [128, 64],
        }
    }
}

# 价值网络配置
value_approximator = {
    "class": ValueApproximator,
    "params": {
        "hidden_layer_sizes": [64, 32],
    }
}

# 网络配置 - 组合编码器、聚合器、解码器、价值网络
from drill.model.tf.network.commander import CommanderNetwork

network = {
    "class": CommanderNetwork,
    "params": {
        "encoder_config": encoders,
        "aggregator_config": aggregator,
        "decoder_config": decoders,
        "value_approximator_config": value_approximator,
    }
}

# 模型配置
model_config = {
    'drone_model': {
        'class': CommanderModelPPO,
        'params': {
            'network': network,  # 使用组合好的网络配置
            'learning_rate': 3e-4,
            'clip_param': 0.2,
            'entropy_coef': 0.01,
            'vf_loss_coef': 0.5,
        },
        # 模型存储参数
        "save": {
            "interval": 100,  # 模型存储间隔
        },
    }
}
