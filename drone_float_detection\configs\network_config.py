from drill.network.interface import NetworkConfig
from drill.network.encoder import <PERSON><PERSON><PERSON><PERSON><PERSON>
from drill.network.aggregator import ConcatAggregator
from drill.network.decoder import MLPDecoder
from drill.network.action_head import DiscreteActionHead
from drill.network.value_head import ValueHead


# 编码器配置
ENCODERS = {
    # 无人机状态编码器
    'drone_state_encoder': NetworkConfig(
        network_class=MLPEncoder,
        network_kwargs={
            'input_keys': ['position', 'orientation', 'velocity', 'angular_velocity', 'battery', 'camera_angle'],
            'hidden_sizes': [64, 64],
            'activation': 'relu',
            'output_size': 64,
        }
    ),

    # 浮漂信息编码器
    'float_info_encoder': NetworkConfig(
        network_class=MLPEncoder,
        network_kwargs={
            'input_keys': ['float_positions', 'float_types', 'float_distances', 'float_detected'],
            'hidden_sizes': [128, 128, 64],
            'activation': 'relu',
            'output_size': 64,
        }
    ),

    # 环境信息编码器
    'env_info_encoder': NetworkConfig(
        network_class=MLPEncoder,
        network_kwargs={
            'input_keys': ['step_count', 'detection_progress'],
            'hidden_sizes': [32, 32],
            'activation': 'relu',
            'output_size': 32,
        }
    ),
}

# 聚合器配置
AGGREGATORS = {
    'main_aggregator': NetworkConfig(
        network_class=ConcatAggregator,
        network_kwargs={
            'input_keys': ['drone_state_encoder', 'float_info_encoder', 'env_info_encoder'],
            'output_size': 160,  # 64 + 64 + 32
        }
    ),
}

# 解码器配置
DECODERS = {
    'policy_decoder': NetworkConfig(
        network_class=MLPDecoder,
        network_kwargs={
            'input_keys': ['main_aggregator'],
            'hidden_sizes': [256, 256, 128],
            'activation': 'relu',
            'output_size': 128,
        }
    ),

    'value_decoder': NetworkConfig(
        network_class=MLPDecoder,
        network_kwargs={
            'input_keys': ['main_aggregator'],
            'hidden_sizes': [256, 128],
            'activation': 'relu',
            'output_size': 64,
        }
    ),
}

# 动作头配置
ACTION_HEADS = {
    # 前后移动动作头
    'action_move_fb': NetworkConfig(
        network_class=DiscreteActionHead,
        network_kwargs={
            'input_keys': ['policy_decoder'],
            'action_size': 3,  # 前进/悬停/后退
            'hidden_sizes': [64],
            'activation': 'relu',
        }
    ),

    # 左右移动动作头
    'action_move_lr': NetworkConfig(
        network_class=DiscreteActionHead,
        network_kwargs={
            'input_keys': ['policy_decoder'],
            'action_size': 3,  # 左移/悬停/右移
            'hidden_sizes': [64],
            'activation': 'relu',
        }
    ),

    # 上下移动动作头
    'action_move_ud': NetworkConfig(
        network_class=DiscreteActionHead,
        network_kwargs={
            'input_keys': ['policy_decoder'],
            'action_size': 3,  # 上升/悬停/下降
            'hidden_sizes': [64],
            'activation': 'relu',
        }
    ),

    # 旋转动作头
    'action_rotate': NetworkConfig(
        network_class=DiscreteActionHead,
        network_kwargs={
            'input_keys': ['policy_decoder'],
            'action_size': 3,  # 左转/不转/右转
            'hidden_sizes': [64],
            'activation': 'relu',
        }
    ),

    # 相机俯仰动作头
    'action_camera_pitch': NetworkConfig(
        network_class=DiscreteActionHead,
        network_kwargs={
            'input_keys': ['policy_decoder'],
            'action_size': 3,  # 上/平/下
            'hidden_sizes': [64],
            'activation': 'relu',
        }
    ),

    # 相机偏航动作头
    'action_camera_yaw': NetworkConfig(
        network_class=DiscreteActionHead,
        network_kwargs={
            'input_keys': ['policy_decoder'],
            'action_size': 3,  # 左/中/右
            'hidden_sizes': [64],
            'activation': 'relu',
        }
    ),
}

# 价值头配置
VALUE_HEADS = {
    'value': NetworkConfig(
        network_class=ValueHead,
        network_kwargs={
            'input_keys': ['value_decoder'],
            'hidden_sizes': [32],
            'activation': 'relu',
        }
    ),
}
