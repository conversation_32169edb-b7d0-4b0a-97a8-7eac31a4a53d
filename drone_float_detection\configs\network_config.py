from drill.network.interface import NetworkConfig
from drill.network.encoder import <PERSON><PERSON><PERSON><PERSON><PERSON>
from drill.network.aggregator import ConcatAggregator
from drill.network.decoder import MLPDecoder
from drill.network.action_head import DiscreteActionHead
from drill.network.value_head import ValueHead


# 编码器配置
ENCODERS = {
    # 无人机状态编码器
    'drone_state_encoder': NetworkConfig(
        network_class=MLPEncoder,
        network_kwargs={
            'input_keys': ['position'],
            'hidden_sizes': [32, 32],
            'activation': 'relu',
            'output_size': 32,
        }
    ),

    # 浮漂信息编码器
    'float_info_encoder': NetworkConfig(
        network_class=MLPEncoder,
        network_kwargs={
            'input_keys': ['float_positions', 'float_types', 'float_distances', 'float_interfered'],
            'hidden_sizes': [64, 64],
            'activation': 'relu',
            'output_size': 64,
        }
    ),

    # 环境信息编码器
    'env_info_encoder': NetworkConfig(
        network_class=MLPEnco<PERSON>,
        network_kwargs={
            'input_keys': ['step_count', 'interference_progress'],
            'hidden_sizes': [16, 16],
            'activation': 'relu',
            'output_size': 16,
        }
    ),
}

# 聚合器配置
AGGREGATORS = {
    'main_aggregator': NetworkConfig(
        network_class=ConcatAggregator,
        network_kwargs={
            'input_keys': ['drone_state_encoder', 'float_info_encoder', 'env_info_encoder'],
            'output_size': 112,  # 32 + 64 + 16
        }
    ),
}

# 解码器配置
DECODERS = {
    'policy_decoder': NetworkConfig(
        network_class=MLPDecoder,
        network_kwargs={
            'input_keys': ['main_aggregator'],
            'hidden_sizes': [128, 64],
            'activation': 'relu',
            'output_size': 64,
        }
    ),

    'value_decoder': NetworkConfig(
        network_class=MLPDecoder,
        network_kwargs={
            'input_keys': ['main_aggregator'],
            'hidden_sizes': [128, 64],
            'activation': 'relu',
            'output_size': 32,
        }
    ),
}

# 动作头配置 - 单一动作头，5个动作
ACTION_HEADS = {
    'action': NetworkConfig(
        network_class=DiscreteActionHead,
        network_kwargs={
            'input_keys': ['policy_decoder'],
            'action_size': 5,  # 上、下、左、右、原地不动
            'hidden_sizes': [32],
            'activation': 'relu',
        }
    ),
}

# 价值头配置
VALUE_HEADS = {
    'value': NetworkConfig(
        network_class=ValueHead,
        network_kwargs={
            'input_keys': ['value_decoder'],
            'hidden_sizes': [16],
            'activation': 'relu',
        }
    ),
}
