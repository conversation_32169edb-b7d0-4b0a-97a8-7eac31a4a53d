# 训练配置文件

# 基础训练参数
TRAINING_CONFIG = {
    # 学习率配置
    'learning_rate': 3e-4,
    'lr_schedule': 'linear',  # 学习率调度策略
    'lr_decay_steps': 1000000,  # 学习率衰减步数

    # 批次配置
    'batch_size': 256,
    'mini_batch_size': 64,
    'buffer_size': 100000,

    # PPO算法参数
    'ppo_epochs': 10,
    'clip_range': 0.2,
    'entropy_coef': 0.01,
    'value_loss_coef': 0.5,
    'max_grad_norm': 0.5,

    # GAE参数
    'gamma': 0.99,  # 折扣因子
    'gae_lambda': 0.95,  # GAE lambda

    # 训练步数
    'total_timesteps': 10000000,
    'n_steps': 2048,  # 每次收集的步数

    # 评估配置
    'eval_freq': 10000,
    'n_eval_episodes': 10,

    # 保存配置
    'save_freq': 50000,
    'save_path': './models/drone_model/',

    # 日志配置
    'log_interval': 100,
    'tensorboard_log': './logs/',

    # 其他配置
    'verbose': 1,
    'seed': 42,
}

# 网络优化器配置
OPTIMIZER_CONFIG = {
    'optimizer': 'adam',
    'adam_eps': 1e-5,
    'weight_decay': 0.0,
}

# 环境配置
ENV_CONFIG = {
    'n_envs': 16,  # 并行环境数量
    'env_kwargs': {
        'render_mode': None,
        'max_episode_steps': 1000,
    }
}

# 回放缓冲区配置
BUFFER_CONFIG = {
    'buffer_size': 100000,
    'n_envs': 16,
    'optimize_memory_usage': False,
}

# 探索配置
EXPLORATION_CONFIG = {
    'exploration_fraction': 0.1,
    'exploration_initial_eps': 1.0,
    'exploration_final_eps': 0.02,
}

# 目标网络配置
TARGET_CONFIG = {
    'target_update_interval': 1000,
    'tau': 1.0,  # 软更新参数
}

# 训练监控配置
MONITOR_CONFIG = {
    'monitor_wrapper': True,
    'info_keywords': ('episode_reward', 'episode_length', 'detected_floats'),
    'allow_early_resets': True,
}

# 检查点配置
CHECKPOINT_CONFIG = {
    'save_freq': 50000,
    'save_path': './checkpoints/',
    'name_prefix': 'drone_float_model',
    'save_replay_buffer': False,
    'save_vecnormalize': True,
}

# 早停配置
EARLY_STOPPING_CONFIG = {
    'patience': 100000,  # 等待步数
    'min_delta': 0.01,   # 最小改进
    'monitor': 'eval/mean_reward',  # 监控指标
    'mode': 'max',  # 最大化还是最小化
}

# 学习率调度配置
LR_SCHEDULE_CONFIG = {
    'schedule': 'linear',
    'initial_value': 3e-4,
    'final_value': 1e-5,
}

# 奖励缩放配置
REWARD_SCALING_CONFIG = {
    'reward_scale': 1.0,
    'normalize_reward': False,
    'clip_reward': False,
    'reward_clip_value': 10.0,
}

# 正则化配置
REGULARIZATION_CONFIG = {
    'l2_reg': 0.0,
    'dropout_rate': 0.0,
    'batch_norm': False,
    'layer_norm': False,
}
