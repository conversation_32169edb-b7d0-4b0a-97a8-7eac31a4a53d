import tkinter as tk
from tkinter import ttk
import matplotlib
matplotlib.use('TkAgg')
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 支持中文
matplotlib.rcParams['axes.unicode_minus'] = False   # 正常显示负号
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import threading
import time
import torch
import numpy as np
from environment import Environment
from ppo_agent import PPOAgent, InterferenceModeSelector
from train_ppo import extract_state_features, point_to_line_segment_distance

class PPOSimulationVisualizer:
    def __init__(self, env, agent, mode_selector, interval=1):
        self.env = env
        self.agent = agent
        self.mode_selector = mode_selector
        self.interval = interval  # 秒
        self.running = False
        self.root = tk.Tk()
        self.root.title("无人机-浮漂仿真可视化 (PPO控制)")
        self.fig, self.ax = plt.subplots(figsize=(10, 10))
        self.canvas = FigureCanvasTkAgg(self.fig, master=self.root)
        self.canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)
        
        # 创建信息框架
        self.info_frame = ttk.Frame(self.root)
        self.info_frame.pack(side=tk.TOP, fill=tk.X)
        
        # 添加时间标签
        self.time_label = ttk.Label(self.info_frame, text="仿真时间: 0s", font=("Arial", 14))
        self.time_label.pack(side=tk.LEFT, padx=10)
        
        # 添加通信质量标签
        self.quality_label = ttk.Label(self.info_frame, text="总通信质量: 0", font=("Arial", 14))
        self.quality_label.pack(side=tk.LEFT, padx=10)
        
        # 添加干扰效果标签
        self.interference_label = ttk.Label(self.info_frame, text="总干扰效果: 0", font=("Arial", 14))
        self.interference_label.pack(side=tk.LEFT, padx=10)

        # 添加干扰链路数标签
        self.affected_links_label = ttk.Label(self.info_frame, text="受干扰链路: 0", font=("Arial", 14))
        self.affected_links_label.pack(side=tk.LEFT, padx=10)
        
        # 控制按钮
        self.control_frame = ttk.Frame(self.root)
        self.control_frame.pack(side=tk.TOP)
        
        self.start_btn = ttk.Button(self.control_frame, text="开始", command=self.start)
        self.start_btn.pack(side=tk.LEFT)
        
        self.pause_btn = ttk.Button(self.control_frame, text="暂停", command=self.pause)
        self.pause_btn.pack(side=tk.LEFT)
        
        self.step_btn = ttk.Button(self.control_frame, text="步进", command=self.step_once)
        self.step_btn.pack(side=tk.LEFT)
        
        self.reset_btn = ttk.Button(self.control_frame, text="重置", command=self.reset)
        self.reset_btn.pack(side=tk.LEFT)
        
        self.quit_btn = ttk.Button(self.control_frame, text="退出", command=self.root.quit)
        self.quit_btn.pack(side=tk.LEFT)
        
        self.sim_time = 0
        self._draw_static()

    def _draw_static(self):
        self.ax.clear()
        self.ax.set_xlim(0, self.env.map_width)
        self.ax.set_ylim(0, self.env.map_height)
        self.ax.set_title("无人机-浮漂仿真可视化 (PPO控制)", fontsize=16)
        # 画浮漂
        bx = [b["x"] for b in self.env.buoy_positions]
        by = [b["y"] for b in self.env.buoy_positions]
        self.ax.scatter(bx, by, c='blue', s=10, label="浮漂")
        # 画无人机
        dx = [v[0] for v in self.env.drone_positions.values()]
        dy = [v[1] for v in self.env.drone_positions.values()]
        self.drones_plot = self.ax.scatter(dx, dy, c='red', s=60, marker='*', label="无人机")
        self.ax.legend()
        self.canvas.draw()

    def _draw_dynamic(self, comms, drone_result):
        self._draw_static()
        
        # 获取受干扰的通信链路
        affected_links = self._get_affected_links(drone_result, comms)
        
        # 画通信对
        for pair in comms:
            b1 = next(b for b in self.env.buoy_positions if b["id"] == pair["buoy1"])
            b2 = next(b for b in self.env.buoy_positions if b["id"] == pair["buoy2"])
            q = pair["quality"]
            
            # 判断该通信对是否受到干扰
            is_affected = any(pair["buoy1"] == link[0] and pair["buoy2"] == link[1] for link in affected_links)
            
            # 干扰链路用红色虚线表示
            if is_affected:
                color = 'red'
                ls = ':'
                alpha = 0.8
                zorder = 3
            else:
                color = plt.cm.viridis(q/100)
                ls = '-'
                alpha = 0.7
                zorder = 1
            
            lw = 0.5 + 2.5 * (q/100)
            self.ax.plot([b1["x"], b2["x"]], [b1["y"], b2["y"]], color=color, linewidth=lw, alpha=alpha, linestyle=ls, zorder=zorder)
            mx = (b1["x"] + b2["x"]) / 2
            my = (b1["y"] + b2["y"]) / 2
            self.ax.text(mx, my, f"{q:.1f}", color='black', fontsize=8, ha='center', va='center', bbox=dict(facecolor='white', alpha=0.5, edgecolor='none', boxstyle='round,pad=0.1'))
        
        # 画无人机（带干扰模式）和干扰范围
        for name, (x, y, mode) in drone_result.items():
            self.ax.scatter([x], [y], c='red', s=80, marker='*', zorder=5)
            self.ax.text(x, y, f"{name}\n干扰{mode}", color='red', fontsize=8, ha='center', va='bottom', zorder=5)
            # 画干扰范围
            circle = plt.Circle((x, y), self.env.interference_range, color='red', fill=False, linestyle='--', alpha=0.3, linewidth=1.5)
            self.ax.add_patch(circle)
        
        # 更新通信质量标签
        total_quality = sum([pair["quality"] for pair in comms])
        self.quality_label.config(text=f"总通信质量: {total_quality:.2f}")
        
        # 计算总干扰效果
        total_interference = 0
        for name, (x, y, mode) in drone_result.items():
            for pair in comms:
                buoy1 = next(b for b in self.env.buoy_positions if b["id"] == pair["buoy1"])
                buoy2 = next(b for b in self.env.buoy_positions if b["id"] == pair["buoy2"])
                
                # 计算无人机到两个浮漂的距离
                dist1 = ((x - buoy1["x"]) ** 2 + (y - buoy1["y"]) ** 2) ** 0.5
                dist2 = ((x - buoy2["x"]) ** 2 + (y - buoy2["y"]) ** 2) ** 0.5
                
                # 计算无人机到通信链路的距离
                dist_to_link = point_to_line_segment_distance(x, y, buoy1["x"], buoy1["y"], buoy2["x"], buoy2["y"])
                
                # 如果至少一个浮漂在干扰范围内或通信链路穿过干扰范围
                if dist1 <= self.env.interference_range or dist2 <= self.env.interference_range or dist_to_link <= self.env.interference_range:
                    comm_mode = pair["mode"]
                    effect = self.env.interference_table[mode-1][comm_mode]
                    total_interference += effect
        
        self.interference_label.config(text=f"总干扰效果: {total_interference:.2f}")
        
        # 更新受干扰链路数量
        self.affected_links_label.config(text=f"受干扰链路: {len(affected_links)}")
        
        self.canvas.draw()
        
    def _get_affected_links(self, drone_result, comms):
        """获取受干扰的通信链路"""
        affected_links = []
        
        for pair in comms:
            buoy1 = next(b for b in self.env.buoy_positions if b["id"] == pair["buoy1"])
            buoy2 = next(b for b in self.env.buoy_positions if b["id"] == pair["buoy2"])
            
            is_affected = False
            
            for name, (x, y, mode) in drone_result.items():
                # 计算无人机到两个浮漂的距离
                dist1 = ((x - buoy1["x"]) ** 2 + (y - buoy1["y"]) ** 2) ** 0.5
                dist2 = ((x - buoy2["x"]) ** 2 + (y - buoy2["y"]) ** 2) ** 0.5
                
                # 计算无人机到通信链路的距离
                dist_to_link = point_to_line_segment_distance(x, y, buoy1["x"], buoy1["y"], buoy2["x"], buoy2["y"])
                
                # 如果至少一个浮漂在干扰范围内或通信链路穿过干扰范围
                if dist1 <= self.env.interference_range or dist2 <= self.env.interference_range or dist_to_link <= self.env.interference_range:
                    is_affected = True
                    break
            
            if is_affected:
                affected_links.append((pair["buoy1"], pair["buoy2"]))
                
        return affected_links

    def start(self):
        if not self.running:
            self.running = True
            threading.Thread(target=self._run_loop, daemon=True).start()

    def pause(self):
        self.running = False

    def step_once(self):
        self.running = False
        self._sim_step()

    def reset(self):
        self.running = False
        self.env.reset()
        self.sim_time = 0
        self.time_label.config(text=f"仿真时间: {self.sim_time}s")
        self._draw_static()

    def _run_loop(self):
        while self.running:
            self._sim_step()
            time.sleep(self.interval)

    def _sim_step(self):
        # 存储每个无人机的动作
        actions = {}
        
        # 为每个无人机选择动作
        for i in range(1, self.env.num_drones+1):
            # 提取状态
            state = extract_state_features(self.env, i)
            
            # 选择动作
            with torch.no_grad():
                state = torch.FloatTensor(state)
                action_probs, _ = self.agent.policy(state)
                action = torch.argmax(action_probs).item()
            
            actions[i] = action
        
        # 选择最佳干扰模式
        best_modes = self.mode_selector.select_best_mode(
            self.env.drone_positions, 
            self.env.buoy_positions, 
            self.env.comm_pairs,
            self.env.interference_range
        )
        
        # 组合动作和干扰模式
        for i in range(1, self.env.num_drones+1):
            drone_name = f"无人机{i}"
            actions[i] = (actions[i], best_modes[drone_name])
        
        # 执行动作
        drone_result, buoys, comms, _ = self.env.step(actions, duration=1)
        self.sim_time = self.env.current_time
        self.time_label.config(text=f"仿真时间: {self.sim_time}s")
        self._draw_dynamic(comms, drone_result)

    def mainloop(self):
        self.root.mainloop()

# 主函数
if __name__ == "__main__":
    # 创建环境
    env = Environment(buoy_json="my_buoys.json", num_drones=6, drone_speed=16.67)
    
    # 状态维度和动作维度
    state_dim = 6 + 10*2 + 5*6  # 6个基本特征 + 10个浮漂*2 + 5个通信对*6
    action_dim = 5  # 上、下、左、右、原地不动
    
    # 创建PPO代理
    agent = PPOAgent(state_dim, action_dim)
    
    # 尝试加载预训练模型
    try:
        agent.policy.load_state_dict(torch.load('models/drone_ppo_final.pth'))
        agent.policy_old.load_state_dict(torch.load('models/drone_ppo_final.pth'))
        print("成功加载预训练模型")
    except:
        print("未找到预训练模型，使用随机初始化模型")
    
    # 创建干扰模式选择器
    mode_selector = InterferenceModeSelector(env.interference_table)
    
    # 创建可视化器
    vis = PPOSimulationVisualizer(env, agent, mode_selector, interval=0.5)
    vis.mainloop() 