from drill.pipeline.interface import ObsData, ActionData, History


def reward_handler(data: ObsData, history: History):

    # History 传历史state  
    """
    实现奖励函数，并根据态势数据计算当前奖励

    :param data: 原始态势信息
    :param history: 历史信息
    :return: 智能体当前获得奖励
    """
    # 在f4v1样例中，环境返回值包括了原始奖励，则在奖励计算时，可以直接使用环境奖励
    # 这里的data是处理后的data
    extra_info_dict = data.extra_info_dict
    if (extra_info_dict is None) or ('reward' not in extra_info_dict) or (not extra_info_dict):
        return {'reward': 0}
    else:
        return {'reward': extra_info_dict['reward']}




def feature_handler(data: ObsData, history: History):
    """
    o2s，实现态势信息到神经网络输入信息的数据转换

    :param data: 原始态势信息
    :param history: 历史信息
    :return: 神经网络输入数据
    """
    my_units_list, ally_feature = [], []

    # 给 history 这个对象动态添加（或更新）一个名为 "last_obs" 的属性，并赋值为 data.obs。
    # setattr(history,"last_obs",data.obs)
    # k 是player_X v是此 player的 属性值 （单个agent的原始信息 ）
    for k, v in data.obs.items():
        if k == 'b_info':            # 敌方信息
            b_info = {
                'b_pos': v['b_pos'],
                'b_visible': v['b_visible']
            }
            continue
        # 对特征进行处理 保证自身特征在前 队友信息在后！！！  
        if k[-1] == data.agent_name[-1]:        
            my_units_list.append(v)                                         
        else:
            if any([item.sum() for item in v.values()]):        # 保证agent活着
                ally_feature.append(v)
        # print(f"k {k}")
        # print(f"data.agent_name {data.agent_name}")
        # print(f"my_units_list {len(my_units_list)}")
        # print(f"ally_feature {len(ally_feature)}")

    my_units_list.extend(ally_feature)              
    name2feature = {
        'my_units': my_units_list,
        'b_info': b_info,
        # 'x_mask': {"x_mask_inputs": [0, 0, 0, 0, 0, 0, 0, 0, 0, 1]}, # mask信息也要通过feature_handler统一传输
    }
    # history.agents_history[data.agent_name] = name2feature # 如果想将obs数据传到action_handler中，可通过该方式实现
    return name2feature


def action_handler(data: ActionData, history: History) -> ActionData:
    """
    a2c，实现网络输出到智能体动作的转换
    注：data.action会用于训练，目前版本不建议a2c在该模块实现，后续版本会对该部分进行修正

    :param data: 原始神经网络输出
    :param history: 历史信息
    :return: 解析后的动作
    """
    # f4v1样例中，环境可以直接接受神经网络输出值，则不需要做动作映射处理
    # data.action.pop('action_x')   # 如果想令某个动作头不参与训练，即valid_action为false，则可以通过该方式实现
    return data


def player_done_process(data: ObsData, history: History):
    """
    在多智能体环境中，经常会出现部分训练单位在episode结束前死亡的情况。
    不同的仿真环境对死亡单位的处理方式不同，有的可能直接传递一个空的状态，有的可能传递一个全0的状态，
    有的可能保留其死亡前的状态，有的可能直接剔除死亡单位。
    为了保障数据流的正确性和智能体模型训练效果，在训练单位死亡后应当不再训练其智能体。
    因此我们需要对死亡单位的数据进行屏蔽处理。

    该方法用于实现屏蔽死亡训练单位数据的功能

    :param data: 原始态势数据
    （注：data为dict类型，包含dict_keys(['player0', 'player1', 'player2', 'player3'])
    info is class 包含:obs 、extra_info_dict 、agent_name  且obs内包含全局的态势信息
    其中'extra_info_dict'包括'episode_done'字段以及其他从Env.step中obs_dict传过来的变量，'episode_done'表示本局对抗是否结束。）
    :param history: 历史信息                                       
    :return: 经过预处理后的态势信息，传给feature_handler           
    """
    pre_agent_dict = {}
    for agent_name, info in data.items():           
        # info is class 包含:obs 、extra_info_dict 、agent_name  且obs内包含全局的态势信息
        # 对齐环境中单位的名字  将playerX ---->  player_X  因为agent_name和info.obs内的名字的不一致
        agent_name_fix = agent_name[:-1] + '_' + agent_name[-1]

        # 本环境中，死亡单位会传递全0的状态，我们可以通过判断状态是否全0来判断单位是否死亡
        # player_done为True表示单位死亡，为False表示单位存活
        # 取出info.obs中当前agent_name_fix的values，即 agent_name_fix 自身的台式信息，判断其属性是不是全0 全0表示死亡
        # item.sum() 对每个特征做 .sum()，如果是数组就求和，如果是单个数值 .sum() 结果就是它本身
        # any([.....]) 检查list中是否存在不为0的元素，有一个不为零即范围True ，所有元素为0，则返回False
        player_done = not any([item.sum() for item in info.obs[agent_name_fix].values()])

        # 一个episode结束时，会立即获取初始状态，无需屏蔽数据，否则会导致KeyError
        # episode没结束时，屏蔽死亡训练单位的数据。
        if info.extra_info_dict.get("episode_done", False) or not player_done:      # 筛掉已经死亡的player
            pre_agent_dict[agent_name] = info

    return pre_agent_dict, history
