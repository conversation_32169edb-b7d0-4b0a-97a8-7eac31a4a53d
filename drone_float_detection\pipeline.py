import numpy as np
from typing import Dict, Any, List
from drill.pipeline.interface import ObsData, ActionData


def feature_handler(obs_data: ObsData) -> Dict[str, np.ndarray]:
    """特征处理函数

    将原始观测数据转换为神经网络可以处理的特征向量

    Parameters
    ----------
    obs_data : ObsData
        原始观测数据

    Returns
    -------
    Dict[str, np.ndarray]
        处理后的特征字典
    """
    obs = obs_data.obs
    features = {}

    # 无人机状态特征
    drone_state = obs['drone']

    # 位置特征 (3维)
    features['position'] = np.array(drone_state['position'], dtype=np.float32)

    # 姿态特征 (3维)
    features['orientation'] = np.array(drone_state['orientation'], dtype=np.float32)

    # 速度特征 (3维)
    features['velocity'] = np.array(drone_state['velocity'], dtype=np.float32)

    # 角速度特征 (3维)
    features['angular_velocity'] = np.array(drone_state['angular_velocity'], dtype=np.float32)

    # 电池电量特征 (1维)
    features['battery'] = np.array([drone_state['battery']], dtype=np.float32)

    # 相机角度特征 (2维)
    features['camera_angle'] = np.array(drone_state['camera_angle'], dtype=np.float32)

    # 浮漂信息特征
    floats_info = obs['floats']
    max_floats = 20  # 最大浮漂数量，用于固定特征维度

    # 浮漂位置特征 (max_floats * 3)
    float_positions = np.zeros((max_floats, 3), dtype=np.float32)
    # 浮漂类型特征 (max_floats,)
    float_types = np.zeros(max_floats, dtype=np.float32)
    # 浮漂距离特征 (max_floats,)
    float_distances = np.zeros(max_floats, dtype=np.float32)
    # 浮漂检测状态 (max_floats,)
    float_detected = np.zeros(max_floats, dtype=np.float32)

    for i, float_info in enumerate(floats_info[:max_floats]):
        float_positions[i] = float_info['position']
        float_types[i] = float_info['type']
        float_distances[i] = float_info['distance']
        float_detected[i] = 1.0 if float_info['detected'] else 0.0

    features['float_positions'] = float_positions.flatten()
    features['float_types'] = float_types
    features['float_distances'] = float_distances
    features['float_detected'] = float_detected

    # 环境信息特征
    env_info = obs['env']
    features['step_count'] = np.array([env_info['step_count']], dtype=np.float32)
    features['detection_progress'] = np.array([
        env_info['detected_count'] / max(env_info['total_floats'], 1)
    ], dtype=np.float32)

    return features


def reward_handler(obs_data: ObsData) -> float:
    """奖励处理函数

    从观测数据中提取奖励信号

    Parameters
    ----------
    obs_data : ObsData
        观测数据

    Returns
    -------
    float
        奖励值
    """
    return obs_data.extra_info_dict.get('reward', 0.0)


def action_handler(action_data: ActionData) -> Dict[str, Any]:
    """动作处理函数

    将神经网络输出的动作转换为环境可以执行的格式

    Parameters
    ----------
    action_data : ActionData
        动作数据

    Returns
    -------
    Dict[str, Any]
        处理后的动作字典
    """
    actions = {}

    # 从动作数据中提取各个动作头的输出
    if hasattr(action_data, 'action_move_fb'):
        actions['action_move_fb'] = int(action_data.action_move_fb)
    if hasattr(action_data, 'action_move_lr'):
        actions['action_move_lr'] = int(action_data.action_move_lr)
    if hasattr(action_data, 'action_move_ud'):
        actions['action_move_ud'] = int(action_data.action_move_ud)
    if hasattr(action_data, 'action_rotate'):
        actions['action_rotate'] = int(action_data.action_rotate)
    if hasattr(action_data, 'action_camera_pitch'):
        actions['action_camera_pitch'] = int(action_data.action_camera_pitch)
    if hasattr(action_data, 'action_camera_yaw'):
        actions['action_camera_yaw'] = int(action_data.action_camera_yaw)

    return actions


def done_handler(obs_data: ObsData) -> bool:
    """结束条件处理函数

    判断当前episode是否结束

    Parameters
    ----------
    obs_data : ObsData
        观测数据

    Returns
    -------
    bool
        是否结束
    """
    return obs_data.extra_info_dict.get('episode_done', False)
