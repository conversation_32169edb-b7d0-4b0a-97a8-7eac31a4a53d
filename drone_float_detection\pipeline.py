import numpy as np
from typing import Dict, Any, List
from drill.pipeline.interface import ObsData, ActionData


def feature_handler(obs_data: ObsData, feature_sets: List) -> Dict[str, np.ndarray]:
    """特征处理函数

    将原始观测数据转换为神经网络可以处理的特征向量

    Parameters
    ----------
    obs_data : ObsData
        原始观测数据
    feature_sets : List
        特征集合列表

    Returns
    -------
    Dict[str, np.ndarray]
        处理后的特征字典
    """
    obs = obs_data.obs
    features = {}

    # 无人机状态特征
    drone_state = obs['drone']
    env_info = obs['env']

    features['drone_state'] = {
        'position': np.array(drone_state['position'], dtype=np.float32),
        'step_count': float(env_info['step_count']),
        'interference_progress': float(env_info['interfered_count'] / max(env_info['total_floats'], 1))
    }

    # 浮漂特征
    floats_info = obs['floats']
    max_floats = 20  # 最大浮漂数量

    # 初始化浮漂特征数组
    float_features = []

    for i in range(max_floats):
        if i < len(floats_info):
            float_info = floats_info[i]
            float_feature = {
                'position': np.array(float_info['position'], dtype=np.float32),
                'type': int(float_info['type']),
                'distance': float(float_info['distance']),
                'interfered': 1 if float_info['interfered'] else 0
            }
        else:
            # 填充空特征
            float_feature = {
                'position': np.array([0.0, 0.0], dtype=np.float32),
                'type': 0,
                'distance': 0.0,
                'interfered': 0
            }
        float_features.append(float_feature)

    features['floats'] = float_features

    return features


def reward_handler(obs_data: ObsData) -> float:
    """奖励处理函数

    从观测数据中提取奖励信号

    Parameters
    ----------
    obs_data : ObsData
        观测数据

    Returns
    -------
    float
        奖励值
    """
    return obs_data.extra_info_dict.get('reward', 0.0)


def action_handler(action_data: ActionData) -> Dict[str, Any]:
    """动作处理函数

    将神经网络输出的动作转换为环境可以执行的格式

    Parameters
    ----------
    action_data : ActionData
        动作数据

    Returns
    -------
    Dict[str, Any]
        处理后的动作字典
    """
    actions = {}

    # 从动作数据中提取动作
    if hasattr(action_data, 'action_decoder'):
        actions['action'] = int(action_data.action_decoder)
    else:
        actions['action'] = 4  # 默认原地不动

    return actions


def done_handler(obs_data: ObsData) -> bool:
    """结束条件处理函数

    判断当前episode是否结束

    Parameters
    ----------
    obs_data : ObsData
        观测数据

    Returns
    -------
    bool
        是否结束
    """
    return obs_data.extra_info_dict.get('episode_done', False)
