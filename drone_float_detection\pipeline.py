import numpy as np
from typing import Dict, Any, List
from drill.pipeline.interface import ObsData, ActionData, History


def feature_handler(obs_data: ObsData, history: History) -> Dict[str, np.ndarray]:
    """特征处理函数 - 简化版本

    将原始观测数据转换为神经网络可以处理的特征向量

    Parameters
    ----------
    obs_data : ObsData
        原始观测数据
    history : History
        历史信息

    Returns
    -------
    Dict[str, np.ndarray]
        处理后的特征字典
    """
    # 简化的特征处理，先让系统能够运行
    try:
        obs = obs_data.obs

        # 无人机状态特征
        drone_state = obs.get('drone', {})
        env_info = obs.get('env', {})

        # 创建简化的特征字典
        features = {}

        # 无人机状态
        position = drone_state.get('position', [0.0, 0.0])
        features['drone_state'] = {
            'position': np.array(position, dtype=np.float32),
            'step_count': float(env_info.get('step_count', 0)),
            'interference_progress': float(env_info.get('interfered_count', 0) / max(env_info.get('total_floats', 1), 1))
        }

        # 浮漂特征 - 简化处理
        floats_info = obs.get('floats', [])
        max_floats = 10  # 减少到10个

        float_features = []
        for i in range(max_floats):
            if i < len(floats_info):
                float_info = floats_info[i]
                float_feature = {
                    'position': np.array(float_info.get('position', [0.0, 0.0]), dtype=np.float32),
                    'type': int(float_info.get('type', 0)),
                    'distance': float(float_info.get('distance', 0.0)),
                    'interfered': 1 if float_info.get('interfered', False) else 0
                }
            else:
                # 填充空特征
                float_feature = {
                    'position': np.array([0.0, 0.0], dtype=np.float32),
                    'type': 0,
                    'distance': 0.0,
                    'interfered': 0
                }
            float_features.append(float_feature)

        features['floats'] = float_features

        return features

    except Exception as e:
        print(f"Feature handler error: {e}")
        # 返回默认特征
        return {
            'drone_state': {
                'position': np.array([0.0, 0.0], dtype=np.float32),
                'step_count': 0.0,
                'interference_progress': 0.0
            },
            'floats': [{
                'position': np.array([0.0, 0.0], dtype=np.float32),
                'type': 0,
                'distance': 0.0,
                'interfered': 0
            } for _ in range(10)]
        }


def reward_handler(obs_data: ObsData, history: History) -> Dict[str, float]:
    """奖励处理函数

    从观测数据中提取奖励信号

    Parameters
    ----------
    obs_data : ObsData
        观测数据
    history : History
        历史信息

    Returns
    -------
    Dict[str, float]
        奖励字典
    """
    return {'reward': obs_data.extra_info_dict.get('reward', 0.0)}


def action_handler(action_data: ActionData) -> Dict[str, Any]:
    """动作处理函数

    将神经网络输出的动作转换为环境可以执行的格式

    Parameters
    ----------
    action_data : ActionData
        动作数据

    Returns
    -------
    Dict[str, Any]
        处理后的动作字典
    """
    actions = {}

    # 从动作数据中提取动作
    if hasattr(action_data, 'action_decoder'):
        actions['action'] = int(action_data.action_decoder)
    else:
        actions['action'] = 4  # 默认原地不动

    return actions


def done_handler(obs_data: ObsData, history: History) -> bool:
    """结束条件处理函数

    判断当前episode是否结束

    Parameters
    ----------
    obs_data : ObsData
        观测数据
    history : History
        历史信息

    Returns
    -------
    bool
        是否结束
    """
    return obs_data.extra_info_dict.get('episode_done', False)
