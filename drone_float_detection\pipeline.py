import numpy as np
from typing import Dict, Any, List, Tuple
from drill.pipeline.interface import ObsData, ActionData, History


def feature_handler(obs_data: ObsData, history: History) -> Dict[str, Any]:
    """特征处理函数 - 按照f4v1_test的格式

    将原始观测数据转换为神经网络可以处理的特征向量

    Parameters
    ----------
    obs_data : ObsData
        原始观测数据
    history : History
        历史信息

    Returns
    -------
    Dict[str, Any]
        处理后的特征字典，键名对应network_config中的特征集名称
    """
    try:
        obs = obs_data.obs

        # 无人机状态特征
        drone_state = obs.get('drone', {})
        env_info = obs.get('env', {})

        # 按照network_config中定义的特征集名称返回
        name2feature = {
            'drone_state': {
                'position': np.array(drone_state.get('position', [0.0, 0.0]), dtype=np.float32),
                'step_count': float(env_info.get('step_count', 0)),
                'interference_progress': float(env_info.get('interfered_count', 0) / max(env_info.get('total_floats', 1), 1))
            }
        }

        # 浮漂特征 - 按照EntityFeatureSet的格式
        floats_info = obs.get('floats', [])
        max_floats = 10  # 对应network_config中的max_length

        float_features = []
        for i in range(max_floats):
            if i < len(floats_info):
                float_info = floats_info[i]
                float_feature = {
                    'position': np.array(float_info.get('position', [0.0, 0.0]), dtype=np.float32),
                    'type': int(float_info.get('type', 0)),
                    'distance': float(float_info.get('distance', 0.0)),
                    'interfered': 1 if float_info.get('interfered', False) else 0
                }
            else:
                # 填充空特征
                float_feature = {
                    'position': np.array([0.0, 0.0], dtype=np.float32),
                    'type': 0,
                    'distance': 0.0,
                    'interfered': 0
                }
            float_features.append(float_feature)

        name2feature['floats'] = float_features

        return name2feature

    except Exception as e:
        print(f"Feature handler error: {e}")
        # 返回默认特征
        return {
            'drone_state': {
                'position': np.array([0.0, 0.0], dtype=np.float32),
                'step_count': 0.0,
                'interference_progress': 0.0
            },
            'floats': [{
                'position': np.array([0.0, 0.0], dtype=np.float32),
                'type': 0,
                'distance': 0.0,
                'interfered': 0
            } for _ in range(10)]
        }


def reward_handler(obs_data: ObsData, history: History) -> Dict[str, float]:
    """奖励处理函数

    从观测数据中提取奖励信号

    Parameters
    ----------
    obs_data : ObsData
        观测数据
    history : History
        历史信息

    Returns
    -------
    Dict[str, float]
        奖励字典
    """
    return {'reward': obs_data.extra_info_dict.get('reward', 0.0)}


def action_handler(action_data: ActionData, history: History) -> ActionData:
    """动作处理函数

    将神经网络输出的动作转换为环境可以执行的格式

    Parameters
    ----------
    action_data : ActionData
        动作数据
    history : History
        历史信息

    Returns
    -------
    ActionData
        处理后的动作数据
    """
    # 按照f4v1_test的方式，直接返回action_data
    # f4v1样例中，环境可以直接接受神经网络输出值，则不需要做动作映射处理
    return action_data


def done_handler(data: Dict[str, ObsData], history: History) -> Tuple[Dict[str, ObsData], History]:
    """结束条件处理函数 - 类似f4v1_test的player_done_process

    在无人机环境中，处理episode结束的逻辑

    Parameters
    ----------
    data : Dict[str, ObsData]
        所有智能体的观测数据
    history : History
        历史信息

    Returns
    -------
    Tuple[Dict[str, ObsData], History]
        处理后的观测数据和历史信息
    """
    pre_agent_dict = {}
    for agent_name, info in data.items():
        # 无人机环境中，智能体不会死亡，所以直接传递所有数据
        # 如果episode结束，也需要传递数据以便正确处理
        pre_agent_dict[agent_name] = info

    return pre_agent_dict, history
