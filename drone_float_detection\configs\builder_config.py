from drill.builder import BPBuilder
from env_interface import DroneFloatEnv
from drill.pipeline.agent_pipeline import AgentPipeline, HandlerSpecies
from drill.pipeline import GlobalPipeline
from pipeline import feature_handler, reward_handler, action_handler, done_handler
# 导入网络配置
from configs.network_config import model_config


# env 配置，如果需要对环境进行配置，可以通过params传递相应参数，并在env创建时使用
env = {"class": DroneFloatEnv, "params": {}}          # 导入环境类

# 智能体名称
AGENT_NAMES = ['drone_agent']

# pipeline 配置
pipeline = {
    # 根据pipeline中实现的feature_handler，action_handler，reward_handler等函数配置智能体训练pipeline
    "drone_pipeline": {
        "class": AgentPipeline,
        "params": {
            "handler_dict": {
                HandlerSpecies.FEATURE: feature_handler,       # 特征函数 （特征提取）
                HandlerSpecies.REWARD: reward_handler,         # 奖励函数
                HandlerSpecies.ACTION: action_handler,         # 动作函数 （动作选择）
            },
            "batch_config": {
                "gamma": 0.99,
                "lamb": 0.95,
            }
        },
    },
    "global": {
        "class": GlobalPipeline,
        "params": {
            "pre_process": done_handler          # 预处理函数，类似f4v1的player_done_process
        }
    }
}

# 智能体与神经网络模型映射关系，支持多智能体
agents = {
    'drone_agent': {
        "model": "drone_model",          # 选择智能体对应网络模型  model_config中
        "pipeline": "drone_pipeline"     # 选择对应pipeline配置  pipeline中
    },
}

builder = BPBuilder(agents, model_config, env, pipeline)
