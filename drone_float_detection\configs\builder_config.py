from drill.builder.interface import BuilderConfig
from drill.agent import PPOAgent
from drill.pipeline import Pipeline
from env_interface import DroneFloatEnv
from pipeline import feature_handler, reward_handler, action_handler, done_handler
from configs.network_config import ENCODERS, AGGREGATORS, DECODERS, ACTION_HEADS, VALUE_HEADS
from configs.training_config import TRAINING_CONFIG


# 智能体名称列表
AGENT_NAMES = ['drone_agent']

# 环境配置
ENV_CONFIG = BuilderConfig(
    builder_class=DroneFloatEnv,
    builder_kwargs={
        'env_id': 0,
        'extra_info': {'index': '0'}
    }
)

# 管道配置
PIPELINE_CONFIG = BuilderConfig(
    builder_class=Pipeline,
    builder_kwargs={
        'feature_handler': feature_handler,
        'reward_handler': reward_handler,
        'action_handler': action_handler,
        'done_handler': done_handler,
    }
)

# 智能体配置
AGENT_CONFIGS = {
    'drone_agent': BuilderConfig(
        builder_class=PPOAgent,
        builder_kwargs={
            'agent_name': 'drone_agent',
            'encoders': ENCODERS,
            'aggregators': AGGREGATORS,
            'decoders': DECODERS,
            'action_heads': ACTION_HEADS,
            'value_heads': VALUE_HEADS,
            'training_config': TRAINING_CONFIG,
        }
    )
}

# 智能体映射配置
AGENT_MAPPING = {
    'drone_agent': ['drone_agent']  # 环境中的智能体名称映射到配置中的智能体名称
}

# 训练器配置
TRAINER_CONFIG = {
    'algorithm': 'PPO',
    'total_timesteps': TRAINING_CONFIG['total_timesteps'],
    'learning_rate': TRAINING_CONFIG['learning_rate'],
    'batch_size': TRAINING_CONFIG['batch_size'],
    'n_steps': TRAINING_CONFIG['n_steps'],
    'gamma': TRAINING_CONFIG['gamma'],
    'gae_lambda': TRAINING_CONFIG['gae_lambda'],
    'clip_range': TRAINING_CONFIG['clip_range'],
    'entropy_coef': TRAINING_CONFIG['entropy_coef'],
    'value_loss_coef': TRAINING_CONFIG['value_loss_coef'],
    'max_grad_norm': TRAINING_CONFIG['max_grad_norm'],
    'ppo_epochs': TRAINING_CONFIG['ppo_epochs'],
    'mini_batch_size': TRAINING_CONFIG['mini_batch_size'],
}

# 评估配置
EVAL_CONFIG = {
    'eval_freq': TRAINING_CONFIG['eval_freq'],
    'n_eval_episodes': TRAINING_CONFIG['n_eval_episodes'],
    'eval_env': ENV_CONFIG,
}

# 日志配置
LOG_CONFIG = {
    'log_interval': TRAINING_CONFIG['log_interval'],
    'tensorboard_log': TRAINING_CONFIG['tensorboard_log'],
    'verbose': TRAINING_CONFIG['verbose'],
}

# 保存配置
SAVE_CONFIG = {
    'save_freq': TRAINING_CONFIG['save_freq'],
    'save_path': TRAINING_CONFIG['save_path'],
}

# 多环境配置
MULTI_ENV_CONFIG = {
    'n_envs': 16,  # 并行环境数量
    'env_configs': [
        BuilderConfig(
            builder_class=DroneFloatEnv,
            builder_kwargs={
                'env_id': i,
                'extra_info': {'index': str(i)}
            }
        ) for i in range(16)
    ]
}

# 实验配置
EXPERIMENT_CONFIG = {
    'experiment_name': 'drone_float_detection',
    'run_name': 'ppo_baseline',
    'tags': ['drone', 'float_detection', 'ppo'],
    'notes': '无人机浮漂检测基线实验',
}

# 超参数搜索配置
HYPERPARAMETER_CONFIG = {
    'search_space': {
        'learning_rate': [1e-4, 3e-4, 1e-3],
        'clip_range': [0.1, 0.2, 0.3],
        'entropy_coef': [0.001, 0.01, 0.1],
        'gamma': [0.95, 0.99, 0.995],
    },
    'search_method': 'grid',  # 'grid', 'random', 'bayesian'
    'n_trials': 10,
}

# 分布式训练配置
DISTRIBUTED_CONFIG = {
    'use_distributed': False,
    'n_workers': 1,
    'backend': 'nccl',  # 'nccl', 'gloo', 'mpi'
}

# 调试配置
DEBUG_CONFIG = {
    'debug_mode': False,
    'profile': False,
    'check_env': True,
    'render': False,
}
