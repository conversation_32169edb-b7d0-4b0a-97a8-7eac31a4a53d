import tkinter as tk
from tkinter import ttk
import matplotlib
matplotlib.use('TkAgg')
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 支持中文
matplotlib.rcParams['axes.unicode_minus'] = False   # 正常显示负号
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import threading
import time

class SimulationVisualizer:
    def __init__(self, env, interval=1):
        self.env = env
        self.interval = interval  # 秒
        self.running = False
        self.root = tk.Tk()
        self.root.title("无人机-浮漂仿真可视化")
        self.fig, self.ax = plt.subplots(figsize=(8, 8))
        self.canvas = FigureCanvasTkAgg(self.fig, master=self.root)
        self.canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)
        self.time_label = ttk.Label(self.root, text="仿真时间: 0s", font=("Arial", 14))
        self.time_label.pack(side=tk.TOP)
        self.control_frame = ttk.Frame(self.root)
        self.control_frame.pack(side=tk.TOP)
        self.start_btn = ttk.Button(self.control_frame, text="开始", command=self.start)
        self.start_btn.pack(side=tk.LEFT)
        self.pause_btn = ttk.Button(self.control_frame, text="暂停", command=self.pause)
        self.pause_btn.pack(side=tk.LEFT)
        self.step_btn = ttk.Button(self.control_frame, text="步进", command=self.step_once)
        self.step_btn.pack(side=tk.LEFT)
        self.quit_btn = ttk.Button(self.control_frame, text="退出", command=self.root.quit)
        self.quit_btn.pack(side=tk.LEFT)
        self.sim_time = 0
        self.actions = None  # 用户可自定义动作生成器
        self._draw_static()

    def _draw_static(self):
        self.ax.clear()
        self.ax.set_xlim(0, self.env.map_width)
        self.ax.set_ylim(0, self.env.map_height)
        self.ax.set_title("无人机-浮漂仿真可视化", fontsize=16)
        # 画浮漂
        bx = [b["x"] for b in self.env.buoy_positions]
        by = [b["y"] for b in self.env.buoy_positions]
        self.ax.scatter(bx, by, c='blue', s=10, label="浮漂")
        # 画无人机
        dx = [v[0] for v in self.env.drone_positions.values()]
        dy = [v[1] for v in self.env.drone_positions.values()]
        self.drones_plot = self.ax.scatter(dx, dy, c='red', s=60, marker='*', label="无人机")
        self.ax.legend()
        self.canvas.draw()

    def _draw_dynamic(self, comms, drone_result):
        self._draw_static()
        # 画通信对
        for pair in comms:
            b1 = next(b for b in self.env.buoy_positions if b["id"] == pair["buoy1"])
            b2 = next(b for b in self.env.buoy_positions if b["id"] == pair["buoy2"])
            q = pair["quality"]
            color = plt.cm.viridis(q/100)
            lw = 0.5 + 2.5 * (q/100)
            self.ax.plot([b1["x"], b2["x"]], [b1["y"], b2["y"]], color=color, linewidth=lw, alpha=0.7)
            mx = (b1["x"] + b2["x"]) / 2
            my = (b1["y"] + b2["y"]) / 2
            self.ax.text(mx, my, f"{q:.1f}", color='black', fontsize=8, ha='center', va='center', bbox=dict(facecolor='white', alpha=0.5, edgecolor='none', boxstyle='round,pad=0.1'))
        # 画无人机（带干扰模式）和干扰范围
        for name, (x, y, mode) in drone_result.items():
            self.ax.scatter([x], [y], c='red', s=80, marker='*')
            self.ax.text(x, y, f"{name}\n干扰{mode}", color='red', fontsize=8, ha='center', va='bottom')
            # 画干扰范围
            circle = plt.Circle((x, y), self.env.interference_range, color='red', fill=False, linestyle='--', alpha=0.3, linewidth=1.5)
            self.ax.add_patch(circle)
        self.canvas.draw()

    def start(self):
        if not self.running:
            self.running = True
            threading.Thread(target=self._run_loop, daemon=True).start()

    def pause(self):
        self.running = False

    def step_once(self):
        self.running = False
        self._sim_step()

    def _run_loop(self):
        while self.running:
            self._sim_step()
            time.sleep(self.interval)

    def _sim_step(self):
        if self.actions:
            actions = self.actions(self.env, self.sim_time)
        else:
            actions = {i+1: (4, 1) for i in range(self.env.num_drones)}
        drone_result, _, comms, _ = self.env.step(actions, duration=1)
        self.sim_time = self.env.current_time
        self.time_label.config(text=f"仿真时间: {self.sim_time}s")
        self._draw_dynamic(comms, drone_result)

    def mainloop(self):
        self.root.mainloop()

if __name__ == "__main__":
    from environment import Environment
    env = Environment(buoy_json="my_buoys.json", num_drones=6, drone_speed=16.67)
    def random_actions(env, t):
        import random
        return {i+1: (random.randint(0,4), random.randint(1,10)) for i in range(env.num_drones)}
    vis = SimulationVisualizer(env)
    vis.actions = random_actions
    vis.mainloop() 