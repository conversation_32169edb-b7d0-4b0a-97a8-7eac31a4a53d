#!/usr/bin/env python3
"""
无人机浮漂干扰项目简单测试脚本
不依赖drill框架的TensorFlow组件
"""

import sys
import os
import numpy as np
import time

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from raw_env.drone_env import make_drone_env
from raw_env.env_def import ACTIONS
from env_interface import DroneFloatEnv


def test_raw_env():
    """测试原始环境"""
    print("=== 测试原始环境 ===")

    # 创建环境
    env = make_drone_env()

    # 重置环境
    obs = env.reset()
    print(f"初始观测: {obs['drone']['position']}")

    # 测试几步随机动作
    for step in range(5):
        action = np.random.randint(0, 5)  # 随机选择动作
        action_name = ACTIONS[action]

        obs, reward, done, info = env.step(action)

        print(f"步骤 {step+1}:")
        print(f"  动作: {action} ({action_name})")
        print(f"  无人机位置: {obs['drone']['position']}")
        print(f"  奖励: {reward:.2f}")
        print(f"  干扰浮漂数: {info['interfered_floats']}")
        print(f"  是否结束: {done}")
        print()

    print("原始环境测试完成!\n")


def test_env_interface():
    """测试环境接口"""
    print("=== 测试环境接口 ===")

    # 创建环境接口
    env = DroneFloatEnv(env_id=0, extra_info={'index': '0'})

    # 重置环境
    obs_dict = env.reset()

    # 获取第一个智能体的观测
    agent_name = list(obs_dict.keys())[0]
    obs_data = obs_dict[agent_name]

    print(f"智能体名称: {agent_name}")
    print(f"初始观测: {obs_data.obs['drone']['position']}")

    # 测试几步随机动作
    for step in range(5):
        # 随机选择动作
        action = np.random.randint(0, 5)

        # 创建命令字典
        command_dict = {agent_name: {'action': action}}

        # 执行动作
        obs_dict, done = env.step(command_dict)

        # 获取观测和奖励
        obs_data = obs_dict[agent_name]
        reward = obs_data.extra_info_dict.get('reward', 0.0)

        print(f"步骤 {step+1}:")
        print(f"  动作: {action} ({ACTIONS[action]})")
        print(f"  无人机位置: {obs_data.obs['drone']['position']}")
        print(f"  奖励: {reward:.2f}")
        print(f"  是否结束: {done}")
        print()

        if done:
            break

    print("环境接口测试完成!\n")


def main():
    """主函数"""
    print("开始无人机浮漂干扰环境测试...")
    print("=" * 50)

    try:
        # 测试原始环境
        test_raw_env()

        # 测试环境接口
        test_env_interface()

        print("=" * 50)
        print("所有测试完成!")

    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
