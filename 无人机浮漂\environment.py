import json
import random
import copy

class Environment:
    def __init__(self, buoy_json="my_buoys.json", num_drones=6, drone_speed=16.67, comm_switch_period=30, comm_max_distance=2000, interference_period=10, interference_range=1000):
        self.buoy_json = buoy_json
        self.num_drones = num_drones
        self.drone_speed = drone_speed
        self.comm_switch_period = comm_switch_period  # 通信切换周期（秒）
        self.comm_max_distance = comm_max_distance   # 通信最大距离
        self.interference_period = interference_period  # 干扰决策周期（秒）
        self.interference_range = interference_range    # 干扰半径
        self._load_buoy_positions()
        self._init_drones()
        self.current_time = 0
        self.last_comm_switch = -comm_switch_period  # 保证初始化时立即配对
        self.comm_pairs = []
        self._init_interference_table()
        self._update_communications(force=True)

    def _load_buoy_positions(self):
        with open(self.buoy_json, 'r', encoding='utf-8') as f:
            data = json.load(f)
            self.map_width = data["map_width"]
            self.map_height = data["map_height"]
            self.buoy_positions = data["buoy_positions"]

    def _init_drones(self):
        self.drone_positions = {}
        self.drone_modes = {}  # 干扰模式，1~10
        for i in range(self.num_drones):
            x = round(random.uniform(0, self.map_width), 2)
            y = round(random.uniform(0, self.map_height), 2)
            self.drone_positions[f"无人机{i+1}"] = (x, y)
            self.drone_modes[f"无人机{i+1}"] = 1  # 默认干扰1

    def _init_interference_table(self):
        # 10x20干扰效果表
        self.interference_table = [
            [0.85,0.10,0.30,0.45,0.60,0.25,0.70,0.15,0.50,0.35,0.20,0.65,0.40,0.55,0.75,0.05,0.80,0.90,0.25,0.50],
            [0.20,0.75,0.85,0.30,0.45,0.60,0.15,0.70,0.25,0.50,0.35,0.10,0.65,0.40,0.55,0.80,0.05,0.30,0.90,0.45],
            [0.50,0.35,0.20,0.95,0.10,0.30,0.45,0.60,0.25,0.70,0.15,0.50,0.35,0.80,0.05,0.40,0.55,0.75,0.65,0.90],
            [0.25,0.70,0.15,0.50,0.85,0.10,0.30,0.45,0.60,0.25,0.70,0.15,0.50,0.35,0.20,0.65,0.40,0.55,0.75,0.05],
            [0.60,0.25,0.70,0.15,0.50,0.95,0.10,0.30,0.45,0.60,0.25,0.70,0.15,0.50,0.35,0.20,0.65,0.40,0.55,0.75],
            [0.35,0.20,0.65,0.40,0.55,0.75,0.05,0.80,0.90,0.25,0.50,0.35,0.20,0.65,0.40,0.55,0.75,0.05,0.80,0.90],
            [0.70,0.15,0.50,0.35,0.20,0.65,0.80,0.05,0.30,0.90,0.45,0.60,0.25,0.70,0.15,0.50,0.35,0.20,0.65,0.40],
            [0.10,0.30,0.45,0.60,0.25,0.70,0.15,0.50,0.35,0.20,0.65,0.40,0.55,0.75,0.05,0.80,0.90,0.25,0.50,0.35],
            [0.45,0.60,0.25,0.70,0.15,0.50,0.35,0.20,0.65,0.40,0.55,0.75,0.05,0.80,0.90,0.25,0.50,0.35,0.20,0.65],
            [0.15,0.50,0.35,0.20,0.65,0.40,0.55,0.75,0.05,0.80,0.90,0.25,0.50,0.35,0.20,0.65,0.40,0.55,0.75,0.05]
        ]

    def _distance(self, b1, b2):
        dx = b1["x"] - b2["x"]
        dy = b1["y"] - b2["y"]
        return (dx ** 2 + dy ** 2) ** 0.5

    def _update_communications(self, force=False):
        if not force and self.current_time - self.last_comm_switch < self.comm_switch_period:
            return
        buoys = self.buoy_positions.copy()
        random.shuffle(buoys)
        used = set()
        pairs = []
        n = len(buoys)
        for i in range(n):
            if buoys[i]["id"] in used:
                continue
            for j in range(i+1, n):
                if buoys[j]["id"] in used:
                    continue
                d = self._distance(buoys[i], buoys[j])
                if d <= self.comm_max_distance:
                    mode = random.randint(0, 19)
                    # 计算通信质量
                    # 默认通信质量为1.0（连通且未被干扰）
                    interference_sum = 0.0
                    for drone_name, (dx, dy) in self.drone_positions.items():
                        drone_mode = self.drone_modes[drone_name]  # 1~10
                        dist1 = ((dx - buoys[i]["x"]) ** 2 + (dy - buoys[i]["y"]) ** 2) ** 0.5
                        dist2 = ((dx - buoys[j]["x"]) ** 2 + (dy - buoys[j]["y"]) ** 2) ** 0.5
                        
                        # 计算无人机到通信链路的距离
                        # 使用点到线段的最短距离公式
                        x1, y1 = buoys[i]["x"], buoys[i]["y"]
                        x2, y2 = buoys[j]["x"], buoys[j]["y"]
                        
                        # 计算向量
                        line_vec = (x2 - x1, y2 - y1)
                        drone_to_buoy1_vec = (dx - x1, dy - y1)
                        
                        # 计算线段长度的平方
                        line_length_squared = line_vec[0]**2 + line_vec[1]**2
                        
                        # 如果线段长度为0（两个浮漂位置相同），直接计算点到点距离
                        if line_length_squared == 0:
                            dist_to_link = ((dx - x1)**2 + (dy - y1)**2)**0.5
                        else:
                            # 计算投影比例
                            t = max(0, min(1, (drone_to_buoy1_vec[0]*line_vec[0] + drone_to_buoy1_vec[1]*line_vec[1]) / line_length_squared))
                            
                            # 计算投影点
                            proj_x = x1 + t * line_vec[0]
                            proj_y = y1 + t * line_vec[1]
                            
                            # 计算距离
                            dist_to_link = ((dx - proj_x)**2 + (dy - proj_y)**2)**0.5
                        
                        # 如果至少一个浮漂在干扰范围内或通信链路穿过干扰范围
                        if dist1 <= self.interference_range or dist2 <= self.interference_range or dist_to_link <= self.interference_range:
                            E_mk = self.interference_table[drone_mode-1][mode]
                            interference_sum += E_mk
                    
                    interference_sum = min(1.0, interference_sum)
                    # 通信质量 = 1 - 干扰量
                    quality = 1.0 - interference_sum
                    # 转换为0-100的整数范围以保持与原始代码兼容性
                    quality_scaled = quality * 100
                    
                    pairs.append({
                        "buoy1": buoys[i]["id"],
                        "buoy2": buoys[j]["id"],
                        "mode": mode,
                        "quality": round(quality_scaled, 4)
                    })
                    used.add(buoys[i]["id"])
                    used.add(buoys[j]["id"])
                    break
        self.comm_pairs = pairs
        self.last_comm_switch = self.current_time

    def step(self, actions, duration=1):
        action_map = {
            0: "上",
            1: "下",
            2: "左",
            3: "右",
            4: "原地不动"
        }
        move_map = {
            "上": (0, 1),
            "下": (0, -1),
            "左": (-1, 0),
            "右": (1, 0),
            "原地不动": (0, 0)
        }
        drone_result = {}
        for k, v in actions.items():
            if isinstance(k, int):
                drone_name = f"无人机{k}"
            else:
                drone_name = k
            if isinstance(v, tuple) and len(v) == 2:
                act, mode = v
                if isinstance(act, int):
                    act_str = action_map.get(act, "原地不动")
                else:
                    act_str = act
                if not (1 <= mode <= 10):
                    mode = 1
            else:
                act_str = action_map.get(v, "原地不动") if isinstance(v, int) else v
                mode = 1
            self.drone_modes[drone_name] = mode
            x, y = self.drone_positions[drone_name]
            dx, dy = move_map.get(act_str, (0, 0))
            x_new = x + dx * self.drone_speed * duration
            y_new = y + dy * self.drone_speed * duration
            x_new = min(max(x_new, 0), self.map_width)
            y_new = min(max(y_new, 0), self.map_height)
            self.drone_positions[drone_name] = (round(x_new, 2), round(y_new, 2))
            drone_result[drone_name] = (round(x_new, 2), round(y_new, 2), mode)
        self.current_time += duration
        self._update_communications()
        # 通信效率列表 (buoy1, buoy2, quality)
        efficiency_list = [(pair['buoy1'], pair['buoy2'], pair['quality']) for pair in self.comm_pairs]
        return copy.deepcopy(drone_result), copy.deepcopy(self.buoy_positions), copy.deepcopy(self.comm_pairs), efficiency_list

    def reset(self):
        self._init_drones()
        self.current_time = 0
        self.last_comm_switch = -self.comm_switch_period
        self._update_communications(force=True)
        efficiency_list = [(pair['buoy1'], pair['buoy2'], pair['quality']) for pair in self.comm_pairs]
        return {
            "无人机": {k: (v[0], v[1], self.drone_modes[k]) for k, v in self.drone_positions.items()},
            "浮漂位置": copy.deepcopy(self.buoy_positions),
            "地图": {"宽度": self.map_width, "高度": self.map_height},
            "浮漂通信": copy.deepcopy(self.comm_pairs),
            "通信效率列表": efficiency_list,
            "通信切换周期": self.comm_switch_period,
            "通信最大距离": self.comm_max_distance,
            "干扰决策周期": self.interference_period,
            "干扰半径": self.interference_range
        }

# 示例用法
if __name__ == "__main__":
    env = Environment(buoy_json="my_buoys.json", num_drones=6, drone_speed=16.67, comm_switch_period=30, comm_max_distance=2000, interference_period=10, interference_range=1000)
    print("初始状态:", env.reset())
    actions = {1: (0, 3), 2: (1, 5), 3: (2, 7), 4: (3, 2), 5: (4, 1), 6: (0, 10)}
    drone_result, buoys, comms, eff_list = env.step(actions, duration=10)
    print("无人机新位置和干扰模式:", drone_result)
    print("浮漂通信对(前5):", comms[:5])
    print("通信效率(前5):", eff_list[:5]) 