from drill.builder import BPBuilder
from env_interface import F4v1Env
from drill.pipeline.agent_pipeline import Agent<PERSON><PERSON>eline, HandlerSpecies
from drill.pipeline import GlobalPipeline
from pipeline import feature_handler, reward_handler, action_handler, player_done_process
# 导入了我方特征 敌方特征 和 model配置
from configs.network_config import entity_feature_set, target_feature_set
from configs.network_config import model_config


# env 配置，如果需要对环境进行配置，可以通过params传递相应参数，并在env创建时使用
env = {"class": F4v1Env, "params": {}}          # 导入环境类

# 智能体名称
AGENT_NAMES = ['player0', 'player1', 'player2', 'player3']

# pipeline 配置
pipeline = {
    # 根据pipeline中实现的feature_handler，action_handler，reward_handler等函数配置智能体训练pipeline
    "f4v1_pipeline": {  
        "class": AgentPipeline,
        "params": {
            "handler_dict": {
                HandlerSpecies.FEATURE: (
                    feature_handler,     # 特征函数 （特征提取）        
                    [
                    entity_feature_set,       # 实体特征
                    target_feature_set,       # 目标特征
                    # x_mask_feature_set,     # 动作掩码特征
                    ]
                    ),
                HandlerSpecies.REWARD: reward_handler,          # 奖励函数
                HandlerSpecies.ACTION: action_handler,          # 动作函数 （动作选择）
            },
            "batch_config": {
                "gamma": 0.99,
                "lamb": 0.95,
            }
        },
    },
    "global": {
        "class": GlobalPipeline,
        "params": {
            "pre_process": player_done_process          # 过滤死亡单位数据 
        }
    }
}

# 智能体与神经网络模型映射关系，支持多智能体
agents = {
    'player0': {
        "model": "f4v1_model",          # 选择智能体对应网络模型  model_config中
        "pipeline": "f4v1_pipeline"     # 选择对应pipeline配置  pipeline中
    },
    'player1': {
        "model": "f4v1_model",
        "pipeline": "f4v1_pipeline"
    },
    'player2': {
        "model": "f4v1_model",
        "pipeline": "f4v1_pipeline"
    },
    'player3': {
        "model": "f4v1_model",
        "pipeline": "f4v1_pipeline"
    },
}

builder = BPBuilder(agents, model_config, env, pipeline)