# F4v1 项⽬说明
该项⽬是在⼀个有限的圆柱体空间内通过⼀定策略操纵4个物体A对1个物体B进⾏探寻并追击。
物体A与物体B的平⾯速度、加速度、垂直速度、活动范围（俯视）、深度、转向速度、视野（物体
A专属）等信息根据客户需求进⾏了限制。

## 1 场景描述
场景描述可以细分为环境设定、单位设定、状态判定、操纵设定以及任务⽬标等五个部分。

### 1.环境设定
该环境半径为3km，深度范围为[-240,0]m。物体A与B均为质点。

### 2.单位设定
A物体的初速度数值与⽅向随机，初始⽔平位置为环境中⼼点，深度为-50m。其⽔平速度范围
为[15.433,25.722]m/s，垂直速度为[-2,2]m/s。A的最⼤⻆度变化θ =(-0.0657v^2+3.5332v-7.3176)/
57.3(radius)，最⼤加速度a=θ ·v。

B物体的初速度与⽅向随机，初始⽔平位置与初始深度均为环境允许的随机数值。其平⾯速度范
围为[0,12]m/s，垂直速度为[-1,1]m/s。B的最⼤⻆度变化θ =(-0.000185v^3-0.006391v^2+0.11501v)/
57.3(radius)，最⼤加速度a=θ ·v。

### 3.状态判定
发现与锁定：当物体B处于物体A运动⽅向15°内范围且空间距离不超过2km时，物体A可以锁定
B。其中，空间距离在1km范围内的锁定概率为100%，空间距离超过1km的锁定概率为10%。当某
⼀A发现B后，所有物体A共享视野信息。

击中：当物体A与物体B的空间距离⼩于30m且垂直距离⼩于30m时，判定A成功击中B。 3km

### 4.操纵设定
操纵指令包括三个参数，X为物体的期望顺时针旋转⻆度（实际旋转⻆度需满⾜⻆度限制），Y
为物体的期望⽔平速度（实际速度需满⾜⽔平速度限制），Dv为物体的期望垂直速度（实际速度需
满⾜垂直速度限制）。

### 5.任务⽬标
整个任务可分为发现前与发现后两个阶段。
第⼀阶段：令A尽快探索并对B进⾏锁定。
第⼆阶段：在某⼀物体A发现B的前提下，令所有的A尽快全部击中B。

## 2 算法设计
### 1.智能体设计
该项⽬以多智能体算法为基础，设计了四个基于深度神经⽹络构建的player分别控制物体A。
每个深度神经⽹络的输⼊包括当前控制物体A的空间坐标、⽔平速度、运动⽅向、垂直速度、速
度限制值、⽣存判定等信息，以及以当前控制物体A作为基本点的其他A的相对空间坐标与基本信
息。

在每⼀个时间戳下，智能体从仿真环境中获取当前的态势，并将其转换为深度神经⽹络的输
⼊。深度神经⽹络根据当前的输⼊数据给出包括旋转⻆度、期望⽔平速度以及期望垂直速度等信息
的输出数据。最后，我们将输出数据进⾏动作解析，并将解析后的结果发送给物体A，控制其在环境
中做出相应动作。

### 2.奖励设计
奖励可分为探索奖励与击中奖励。在锁定前，智能体将根据探索到物体B与否获取奖励值；在发
现物体B后，智能体根据探索到B的时间⻓短与撞击速度获取奖励值。⽬前的奖励设定A越早发现B，
获取奖励越⾼；在发现B的前提下，A越早击中B，奖励越⾼。

### 3.智能体训练
该项⽬使⽤了PPO算法对智能体进⾏了训练，PPO是⼀种基于多智能体的深度强化学习算法。
智能体通过收集物体A与环境进⾏交互的数据与对应奖励值对深度神经⽹络的参数进⾏持续更新。

## 3 效果说明
a.⿊⾊代表物体A，红⾊代表物体B，对应空间坐标⻅⽩⾊⽂字。

b.右上⽅为物体A的实时速度，⽩⾊代表尚未击中B，红⾊代表已经击中B。

c.智能体每5秒决策⼀次，共200步，即1000秒为⼀局。

d.击中判定条件为物体A与物体B的空间距离为30m内且垂直距离为30m内。

e.图像上⽅为俯视图，下⽅为侧视图，A的探测区域以扇形表示（半径为1km）。

f.当某⼀A击中B时，图中对应点由⿊⾊变为灰⾊。

g.当A与B的平⾯距离较近⽽垂直距离较远时，A会绕B旋转并同时进⾏上浮或下潜运动，直⾄其
满⾜击中判定。